plugins {
    java
    id("io.spring.dependency-management")
}

allprojects {
    repositories {
        mavenCentral()
        maven {
            url = uri("https://maven.aliyun.com/repository/public")
            isAllowInsecureProtocol = true
        }
    }
}


subprojects {
    apply(plugin = "java") // 确保所有子模块应用 Java 插件
    apply(plugin = "io.spring.dependency-management")

    group = "lyy"
    version = "1.0"


    val netflixDgsVersion: String by extra("10.1.2")
    val springShellVersion: String by extra("3.4.0")

    dependencyManagement {
        dependencies {
            imports {
                mavenBom("com.netflix.graphql.dgs:graphql-dgs-platform-dependencies:$netflixDgsVersion")
                mavenBom("org.springframework.shell:spring-shell-dependencies:${springShellVersion}")
            }
        }
    }

    java {
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(21)) // 统一使用 Java 21
        }
    }
}
