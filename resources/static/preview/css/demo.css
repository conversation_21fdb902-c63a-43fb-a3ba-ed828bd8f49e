pre.highlight,
.highlight pre {
  max-height: 30rem;
  margin: 1.5rem 0;
  overflow: auto;
  border-radius: var(--tblr-border-radius);
}

.highlight {
  margin: 0;
}
.highlight code > * {
  margin: 0 !important;
  padding: 0 !important;
}
.highlight .c, .highlight .c1 {
  color: #a0aec0;
}
.highlight .nt, .highlight .nc, .highlight .nx {
  color: #ff8383;
}
.highlight .na, .highlight .p {
  color: #ffe484;
}
.highlight .s, .highlight .dl, .highlight .s2 {
  color: #b5f4a5;
}
.highlight .k {
  color: #93ddfd;
}
.highlight .s1, .highlight .mi {
  color: #d9a9ff;
}

.dropdown-menu-demo {
  display: inline-block;
  width: 100%;
  position: relative;
  top: 0;
  margin-bottom: 1rem !important;
}

.demo-icon-preview {
  position: sticky;
  top: 0;
}
.demo-icon-preview svg,
.demo-icon-preview i {
  width: 15rem;
  height: 15rem;
  font-size: 15rem;
  stroke-width: 1.5;
  margin: 0 auto;
  display: block;
}

.demo-icon-preview-icon pre {
  margin: 0;
  user-select: all;
}

.demo-dividers > p {
  opacity: 0.2;
  user-select: none;
}

.demo-icons-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 -2px -1px 0;
  list-style: none;
}
.demo-icons-list > * {
  flex: 1 0 4rem;
}

.demo-icons-list-wrap {
  overflow: hidden;
}

.demo-icons-list-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1;
  text-align: center;
  padding: 0.5rem;
  border-right: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);
  border-bottom: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);
  color: inherit;
  cursor: pointer;
}
.demo-icons-list-item .icon {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 1.5rem;
}
.demo-icons-list-item:hover {
  text-decoration: none;
}

/*# sourceMappingURL=data:application/json;base64,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 */