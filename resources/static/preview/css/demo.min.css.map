{"version": 3, "sources": ["dist/preview/css/demo.css", "demo.css"], "names": [], "mappings": "AACA,eCDA,cDEE,WAAY,MACZ,OAAQ,OAAO,EACf,SAAU,KACV,cAAe,0BAGjB,WACE,OAAQ,EAEV,kBACE,OAAQ,YACR,QAAS,YAEX,cAAe,eACb,MAAO,QAEO,eAAhB,eAAgC,eAC9B,MAAO,QAET,eAAgB,cACd,MAAO,QAEM,eAAf,cAA+B,eAC7B,MAAO,QAET,cACE,MAAO,QAEO,eAAhB,eACE,MAAO,QAGT,oBACE,QAAS,aACT,MAAO,KACP,SAAU,SACV,IAAK,EACL,cAAe,eAGjB,mBACE,SAAU,OACV,IAAK,EAGP,qBADA,uBAEE,MAAO,MACP,OAAQ,MACR,UAAW,MACX,aAAc,IACd,OAAQ,EAAE,KACV,QAAS,MAGX,4BACE,OAAQ,EACR,YAAa,IAGf,iBACE,QAAS,GACT,YAAa,KAGf,iBACE,QAAS,KACT,UAAW,KACX,QAAS,EACT,OAAQ,EAAE,KAAK,KAAK,EACpB,WAAY,KAEd,mBACE,KAAM,EAAE,EAAE,KAGZ,sBACE,SAAU,OAGZ,sBACE,QAAS,KACT,eAAgB,OAChB,YAAa,OACb,gBAAiB,OACjB,aAAc,EACd,WAAY,OACZ,QAAS,MACT,aAAc,yBAAyB,yBAAyB,yBAChE,cAAe,yBAAyB,yBAAyB,yBACjE,MAAO,QACP,OAAQ,QAEV,4BACE,MAAO,OACP,OAAQ,OACR,UAAW,OAEb,4BACE,gBAAiB", "sourcesContent": ["pre.highlight,\n.highlight pre {\n  max-height: 30rem;\n  margin: 1.5rem 0;\n  overflow: auto;\n  border-radius: var(--tblr-border-radius);\n}\n\n.highlight {\n  margin: 0;\n}\n.highlight code > * {\n  margin: 0 !important;\n  padding: 0 !important;\n}\n.highlight .c, .highlight .c1 {\n  color: #a0aec0;\n}\n.highlight .nt, .highlight .nc, .highlight .nx {\n  color: #ff8383;\n}\n.highlight .na, .highlight .p {\n  color: #ffe484;\n}\n.highlight .s, .highlight .dl, .highlight .s2 {\n  color: #b5f4a5;\n}\n.highlight .k {\n  color: #93ddfd;\n}\n.highlight .s1, .highlight .mi {\n  color: #d9a9ff;\n}\n\n.dropdown-menu-demo {\n  display: inline-block;\n  width: 100%;\n  position: relative;\n  top: 0;\n  margin-bottom: 1rem !important;\n}\n\n.demo-icon-preview {\n  position: sticky;\n  top: 0;\n}\n.demo-icon-preview svg,\n.demo-icon-preview i {\n  width: 15rem;\n  height: 15rem;\n  font-size: 15rem;\n  stroke-width: 1.5;\n  margin: 0 auto;\n  display: block;\n}\n\n.demo-icon-preview-icon pre {\n  margin: 0;\n  user-select: all;\n}\n\n.demo-dividers > p {\n  opacity: 0.2;\n  user-select: none;\n}\n\n.demo-icons-list {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 0;\n  margin: 0 -2px -1px 0;\n  list-style: none;\n}\n.demo-icons-list > * {\n  flex: 1 0 4rem;\n}\n\n.demo-icons-list-wrap {\n  overflow: hidden;\n}\n\n.demo-icons-list-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  aspect-ratio: 1;\n  text-align: center;\n  padding: 0.5rem;\n  border-right: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  border-bottom: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  color: inherit;\n  cursor: pointer;\n}\n.demo-icons-list-item .icon {\n  width: 1.5rem;\n  height: 1.5rem;\n  font-size: 1.5rem;\n}\n.demo-icons-list-item:hover {\n  text-decoration: none;\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */", "pre.highlight,\n.highlight pre {\n  max-height: 30rem;\n  margin: 1.5rem 0;\n  overflow: auto;\n  border-radius: var(--tblr-border-radius);\n}\n\n.highlight {\n  margin: 0;\n}\n.highlight code > * {\n  margin: 0 !important;\n  padding: 0 !important;\n}\n.highlight .c, .highlight .c1 {\n  color: #a0aec0;\n}\n.highlight .nt, .highlight .nc, .highlight .nx {\n  color: #ff8383;\n}\n.highlight .na, .highlight .p {\n  color: #ffe484;\n}\n.highlight .s, .highlight .dl, .highlight .s2 {\n  color: #b5f4a5;\n}\n.highlight .k {\n  color: #93ddfd;\n}\n.highlight .s1, .highlight .mi {\n  color: #d9a9ff;\n}\n\n.dropdown-menu-demo {\n  display: inline-block;\n  width: 100%;\n  position: relative;\n  top: 0;\n  margin-bottom: 1rem !important;\n}\n\n.demo-icon-preview {\n  position: sticky;\n  top: 0;\n}\n.demo-icon-preview svg,\n.demo-icon-preview i {\n  width: 15rem;\n  height: 15rem;\n  font-size: 15rem;\n  stroke-width: 1.5;\n  margin: 0 auto;\n  display: block;\n}\n\n.demo-icon-preview-icon pre {\n  margin: 0;\n  user-select: all;\n}\n\n.demo-dividers > p {\n  opacity: 0.2;\n  user-select: none;\n}\n\n.demo-icons-list {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 0;\n  margin: 0 -2px -1px 0;\n  list-style: none;\n}\n.demo-icons-list > * {\n  flex: 1 0 4rem;\n}\n\n.demo-icons-list-wrap {\n  overflow: hidden;\n}\n\n.demo-icons-list-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  aspect-ratio: 1;\n  text-align: center;\n  padding: 0.5rem;\n  border-right: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  border-bottom: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  color: inherit;\n  cursor: pointer;\n}\n.demo-icons-list-item .icon {\n  width: 1.5rem;\n  height: 1.5rem;\n  font-size: 1.5rem;\n}\n.demo-icons-list-item:hover {\n  text-decoration: none;\n}\n"]}