{"version": 3, "file": "demo.js", "sources": ["../../../js/demo.js"], "sourcesContent": ["// Setting items\nconst items = {\n\t\"menu-position\": { localStorage: \"tablerMenuPosition\", default: \"top\" },\n\t\"menu-behavior\": { localStorage: \"tablerMenuBehavior\", default: \"sticky\" },\n\t\"container-layout\": {\n\t\tlocalStorage: \"tablerContainerLayout\",\n\t\tdefault: \"boxed\",\n\t},\n}\n\n// Theme config\nconst config = {}\nfor (const [key, params] of Object.entries(items)) {\n\tconst lsParams = localStorage.getItem(params.localStorage)\n\tconfig[key] = lsParams ? lsParams : params.default\n}\n\n// Parse url params\nconst parseUrl = () => {\n\tconst search = window.location.search.substring(1)\n\tconst params = search.split(\"&\")\n\n\tfor (let i = 0; i < params.length; i++) {\n\t\tconst arr = params[i].split(\"=\")\n\t\tconst key = arr[0]\n\t\tconst value = arr[1]\n\n\t\tif (!!items[key]) {\n\t\t\t// Save to localStorage\n\t\t\tlocalStorage.setItem(items[key].localStorage, value)\n\n\t\t\t// Update local variables\n\t\t\tconfig[key] = value\n\t\t}\n\t}\n}\n\n// Toggle form controls\nconst toggleFormControls = (form) => {\n\tfor (const [key, params] of Object.entries(items)) {\n\t\tconst elem = form.querySelector(\n\t\t\t`[name=\"settings-${key}\"][value=\"${config[key]}\"]`,\n\t\t)\n\n\t\tif (elem) {\n\t\t\telem.checked = true\n\t\t}\n\t}\n}\n\n// Submit form\nconst submitForm = (form) => {\n\t// Save data to localStorage\n\tfor (const [key, params] of Object.entries(items)) {\n\t\t// Save to localStorage\n\t\tconst value = form.querySelector(`[name=\"settings-${key}\"]:checked`).value\n\t\tlocalStorage.setItem(params.localStorage, value)\n\n\t\t// Update local variables\n\t\tconfig[key] = value\n\t}\n\n\twindow.dispatchEvent(new Event(\"resize\"))\n\n\tnew bootstrap.Offcanvas(form).hide()\n}\n\n// Parse url\nparseUrl()\n\n// Elements\nconst form = document.querySelector(\"#offcanvasSettings\")\n\n// Toggle form controls\nif (form) {\n\tform.addEventListener(\"submit\", function (e) {\n\t\te.preventDefault()\n\n\t\tsubmitForm(form)\n\t})\n\n\ttoggleFormControls(form)\n}\n"], "names": ["items", "localStorage", "default", "config", "key", "params", "Object", "entries", "lsParams", "getItem", "parseUrl", "search", "window", "location", "substring", "split", "i", "length", "arr", "value", "setItem", "toggleFormControls", "form", "elem", "querySelector", "checked", "submitForm", "dispatchEvent", "Event", "bootstrap", "<PERSON><PERSON><PERSON>", "hide", "document", "addEventListener", "e", "preventDefault"], "mappings": ";;;;;;AAAA;AACA,MAAMA,KAAK,GAAG;AACb,EAAA,eAAe,EAAE;AAAEC,IAAAA,YAAY,EAAE,oBAAoB;AAAEC,IAAAA,OAAO,EAAE;GAAO;AACvE,EAAA,eAAe,EAAE;AAAED,IAAAA,YAAY,EAAE,oBAAoB;AAAEC,IAAAA,OAAO,EAAE;GAAU;AAC1E,EAAA,kBAAkB,EAAE;AACnBD,IAAAA,YAAY,EAAE,uBAAuB;AACrCC,IAAAA,OAAO,EAAE;AACV;AACD,CAAC;;AAED;AACA,MAAMC,MAAM,GAAG,EAAE;AACjB,KAAK,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE;EAClD,MAAMQ,QAAQ,GAAGP,YAAY,CAACQ,OAAO,CAACJ,MAAM,CAACJ,YAAY,CAAC;EAC1DE,MAAM,CAACC,GAAG,CAAC,GAAGI,QAAQ,GAAGA,QAAQ,GAAGH,MAAM,CAACH,OAAO;AACnD;;AAEA;AACA,MAAMQ,QAAQ,GAAGA,MAAM;EACtB,MAAMC,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAACG,SAAS,CAAC,CAAC,CAAC;AAClD,EAAA,MAAMT,MAAM,GAAGM,MAAM,CAACI,KAAK,CAAC,GAAG,CAAC;AAEhC,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,MAAM,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAME,GAAG,GAAGb,MAAM,CAACW,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC;AAChC,IAAA,MAAMX,GAAG,GAAGc,GAAG,CAAC,CAAC,CAAC;AAClB,IAAA,MAAMC,KAAK,GAAGD,GAAG,CAAC,CAAC,CAAC;AAEpB,IAAA,IAAI,CAAC,CAAClB,KAAK,CAACI,GAAG,CAAC,EAAE;AACjB;MACAH,YAAY,CAACmB,OAAO,CAACpB,KAAK,CAACI,GAAG,CAAC,CAACH,YAAY,EAAEkB,KAAK,CAAC;;AAEpD;AACAhB,MAAAA,MAAM,CAACC,GAAG,CAAC,GAAGe,KAAK;AACpB;AACD;AACD,CAAC;;AAED;AACA,MAAME,kBAAkB,GAAIC,IAAI,IAAK;AACpC,EAAA,KAAK,MAAM,CAAClB,GAAG,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE;AAClD,IAAA,MAAMuB,IAAI,GAAGD,IAAI,CAACE,aAAa,CAC9B,CAAA,gBAAA,EAAmBpB,GAAG,CAAA,UAAA,EAAaD,MAAM,CAACC,GAAG,CAAC,IAC/C,CAAC;AAED,IAAA,IAAImB,IAAI,EAAE;MACTA,IAAI,CAACE,OAAO,GAAG,IAAI;AACpB;AACD;AACD,CAAC;;AAED;AACA,MAAMC,UAAU,GAAIJ,IAAI,IAAK;AAC5B;AACA,EAAA,KAAK,MAAM,CAAClB,GAAG,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE;AAClD;IACA,MAAMmB,KAAK,GAAGG,IAAI,CAACE,aAAa,CAAC,CAAA,gBAAA,EAAmBpB,GAAG,CAAA,UAAA,CAAY,CAAC,CAACe,KAAK;IAC1ElB,YAAY,CAACmB,OAAO,CAACf,MAAM,CAACJ,YAAY,EAAEkB,KAAK,CAAC;;AAEhD;AACAhB,IAAAA,MAAM,CAACC,GAAG,CAAC,GAAGe,KAAK;AACpB;EAEAP,MAAM,CAACe,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;EAEzC,IAAIC,SAAS,CAACC,SAAS,CAACR,IAAI,CAAC,CAACS,IAAI,EAAE;AACrC,CAAC;;AAED;AACArB,QAAQ,EAAE;;AAEV;AACA,MAAMY,IAAI,GAAGU,QAAQ,CAACR,aAAa,CAAC,oBAAoB,CAAC;;AAEzD;AACA,IAAIF,IAAI,EAAE;AACTA,EAAAA,IAAI,CAACW,gBAAgB,CAAC,QAAQ,EAAE,UAAUC,CAAC,EAAE;IAC5CA,CAAC,CAACC,cAAc,EAAE;IAElBT,UAAU,CAACJ,IAAI,CAAC;AACjB,GAAC,CAAC;EAEFD,kBAAkB,CAACC,IAAI,CAAC;AACzB"}