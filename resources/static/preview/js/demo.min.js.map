{"version": 3, "names": ["items", "localStorage", "default", "config", "key", "params", "Object", "entries", "lsParams", "getItem", "parseUrl", "window", "location", "search", "substring", "split", "i", "length", "arr", "value", "setItem", "toggleFormControls", "form", "elem", "querySelector", "checked", "submitForm", "dispatchEvent", "Event", "bootstrap", "<PERSON><PERSON><PERSON>", "hide", "document", "addEventListener", "e", "preventDefault"], "sources": ["../../../js/demo.js"], "sourcesContent": ["// Setting items\nconst items = {\n\t\"menu-position\": { localStorage: \"tablerMenuPosition\", default: \"top\" },\n\t\"menu-behavior\": { localStorage: \"tablerMenuBehavior\", default: \"sticky\" },\n\t\"container-layout\": {\n\t\tlocalStorage: \"tablerContainerLayout\",\n\t\tdefault: \"boxed\",\n\t},\n}\n\n// Theme config\nconst config = {}\nfor (const [key, params] of Object.entries(items)) {\n\tconst lsParams = localStorage.getItem(params.localStorage)\n\tconfig[key] = lsParams ? lsParams : params.default\n}\n\n// Parse url params\nconst parseUrl = () => {\n\tconst search = window.location.search.substring(1)\n\tconst params = search.split(\"&\")\n\n\tfor (let i = 0; i < params.length; i++) {\n\t\tconst arr = params[i].split(\"=\")\n\t\tconst key = arr[0]\n\t\tconst value = arr[1]\n\n\t\tif (!!items[key]) {\n\t\t\t// Save to localStorage\n\t\t\tlocalStorage.setItem(items[key].localStorage, value)\n\n\t\t\t// Update local variables\n\t\t\tconfig[key] = value\n\t\t}\n\t}\n}\n\n// Toggle form controls\nconst toggleFormControls = (form) => {\n\tfor (const [key, params] of Object.entries(items)) {\n\t\tconst elem = form.querySelector(\n\t\t\t`[name=\"settings-${key}\"][value=\"${config[key]}\"]`,\n\t\t)\n\n\t\tif (elem) {\n\t\t\telem.checked = true\n\t\t}\n\t}\n}\n\n// Submit form\nconst submitForm = (form) => {\n\t// Save data to localStorage\n\tfor (const [key, params] of Object.entries(items)) {\n\t\t// Save to localStorage\n\t\tconst value = form.querySelector(`[name=\"settings-${key}\"]:checked`).value\n\t\tlocalStorage.setItem(params.localStorage, value)\n\n\t\t// Update local variables\n\t\tconfig[key] = value\n\t}\n\n\twindow.dispatchEvent(new Event(\"resize\"))\n\n\tnew bootstrap.Offcanvas(form).hide()\n}\n\n// Parse url\nparseUrl()\n\n// Elements\nconst form = document.querySelector(\"#offcanvasSettings\")\n\n// Toggle form controls\nif (form) {\n\tform.addEventListener(\"submit\", function (e) {\n\t\te.preventDefault()\n\n\t\tsubmitForm(form)\n\t})\n\n\ttoggleFormControls(form)\n}\n"], "mappings": ";;;;;;AACA,MAAMA,MAAQ,CACb,gBAAiB,CAAEC,aAAc,qBAAsBC,QAAS,OAChE,gBAAiB,CAAED,aAAc,qBAAsBC,QAAS,UAChE,mBAAoB,CACnBD,aAAc,wBACdC,QAAS,UAKLC,OAAS,GACf,IAAK,MAAOC,EAAKC,KAAWC,OAAOC,QAAQP,OAAQ,CAClD,MAAMQ,EAAWP,aAAaQ,QAAQJ,EAAOJ,cAC7CE,OAAOC,GAAOI,GAAsBH,EAAOH,OAC5C,CAGA,MAAMQ,SAAWA,KAChB,MACML,EADSM,OAAOC,SAASC,OAAOC,UAAU,GAC1BC,MAAM,KAE5B,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAOY,OAAQD,IAAK,CACvC,MAAME,EAAMb,EAAOW,GAAGD,MAAM,KACtBX,EAAMc,EAAI,GACVC,EAAQD,EAAI,GAEZlB,MAAMI,KAEXH,aAAamB,QAAQpB,MAAMI,GAAKH,aAAckB,GAG9ChB,OAAOC,GAAOe,EAEhB,GAIKE,mBAAsBC,IAC3B,IAAK,MAAOlB,EAAKC,KAAWC,OAAOC,QAAQP,OAAQ,CAClD,MAAMuB,EAAOD,EAAKE,cACjB,mBAAmBpB,cAAgBD,OAAOC,QAGvCmB,IACHA,EAAKE,SAAU,EAEjB,GAIKC,WAAcJ,IAEnB,IAAK,MAAOlB,EAAKC,KAAWC,OAAOC,QAAQP,OAAQ,CAElD,MAAMmB,EAAQG,EAAKE,cAAc,mBAAmBpB,eAAiBe,MACrElB,aAAamB,QAAQf,EAAOJ,aAAckB,GAG1ChB,OAAOC,GAAOe,CACf,CAEAR,OAAOgB,cAAc,IAAIC,MAAM,WAE/B,IAAIC,UAAUC,UAAUR,GAAMS,MAAM,EAIrCrB,WAGA,MAAMY,KAAOU,SAASR,cAAc,sBAGhCF,OACHA,KAAKW,iBAAiB,UAAU,SAAUC,GACzCA,EAAEC,iBAEFT,WAAWJ,KACZ,IAEAD,mBAAmBC", "ignoreList": []}