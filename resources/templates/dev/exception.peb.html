{% extends "/layout/layout-content.peb.html" %}

{% block block_body %}
<!-- BEGIN PAGE HEADER -->
<div class="d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <h2 class="page-title">Error</h2>
            </div>
        </div>
    </div>
</div>
<!-- END PAGE HEADER -->
<!-- BEGIN PAGE BODY -->
<div class="page-body">
    <div class="container-xl">
        <div class="card mb-1">
            <div class="card-body">
                <h4>{{ "访问页面"|trans }}</h4>
                <div>
                    <pre><code>{{ request.method }} <a class="text-reset" target="_blank" href="https://preview.tabler.io">{{ request.RequestURI }}</a></code></pre>
                </div>
            </div>
        </div>
        <div class="card mb-1">
            <div class="card-body">
                <h4>Cookies</h4>
                <div>
                    <div class="table-responsive">
                        <table class="table table-vcenter table-bordered">
                            <thead>
                            <tr>
                                <th>Name</th>
                                <th>Value</th>
                                <th>Path</th>
                                <th>Domain</th>
                                <th>Secure</th>
                                <th>Expires At</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for cookie in request.cookies %}
                                <tr>
                                    <td>{{ cookie.name }}</td>
                                    <td class="text-secondary">{{ cookie.value }}</td>
                                    <td class="text-secondary">{{ cookie.path }}</td>
                                    <td class="text-secondary">{{ cookie.domain }}</td>
                                    <td class="text-secondary">{{ cookie.Secure }}</td>
                                    <td class="text-secondary">{{ cookie.maxAge }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mb-1">
            <div class="card-body">
                <h4>StackTrace</h4>
                <div>
                    {% if exception != null %}
                        <h5>{{ exception.message }}</h5>
                    {% endif %}
                    {% if stackTrace != null %}
                        <pre>{{ stackTrace }}</pre>
                    {% else %}
                        <pre>
                                NO STACK TRACE
                        </pre>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer">
                <h4>Escalation</h4>
                <div>Entire team</div>
            </div>
        </div>
    </div>
    </div>

{% endblock %}