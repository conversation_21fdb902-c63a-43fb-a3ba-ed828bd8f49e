{% extends "/layout/layout-content.peb.html" %}
{% set title = "Render Tools" %}
{#{% block block_body %}#}

{#{% endblock %}#}
{% block block_body %}
<div class="container-fluid d-flex flex-md-column flex-fill h-100 g-0" >
    <div class="row justify-content-md-center">
        <div class="col-6">
            <form class="row d-flex align-items-center g-0">
                <div class="col-12">
                    <div class="input-group">
                        <div class="input-group-text">TemplateName</div>
                        <input type="text" value="/dev/exception" name="templateName" class="form-control" id="templateName" placeholder="Template Name">
                        <button id="btnRender" type="button"  class="btn btn-primary">Render</button>
                    </div>
                </div>
            </form>
        </div>

    </div>


    <div class="row g-0  d-flex flex-md-column flex-fill">
        <div id="tabContentViews" class="col" style="border: solid 1px gray;" >
            <iframe id="dev_render_view" class="contentView" src="{{ renderUrl }}" height="100%" width="100%" ></iframe>
        </div>
    </div>
</div>
<script>
$(() => {
    function doRender() {
        let templateName = $('#templateName').val().trim();
        let slash = templateName.startsWith('/') ? '' : '/'
        let url = "{{ request.contextPath }}/dev/render" + slash + templateName;
        console.log('url=', url);
        $('#dev_render_view').attr('src', url);
    }

    $('#btnRender').click();
    $('#btnRender').on('keydown', function(e) {
        debugger
        if (e.which === 13) {
            e.stopPropagation();
            e.preventDefault();
            console.log('Enter pressed in input');
            doRender();
        }
    });
});
</script>
{% endblock %}
{% block block_footer %}
{#   NO FOOTER#}
{% endblock %}
