<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>HOME</title>
    <!-- BEGIN PAGE LEVEL STYLES -->
    <link href="./libs/jsvectormap/dist/jsvectormap.css?1746001501" rel="stylesheet" />
    <!-- END PAGE LEVEL STYLES -->
    {% include "/shared/_common_css.peb.html" %}
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
        }
        aside {
            height: calc(100vh - 64px); /* 100% 视口高度 - header 高度*/
            overflow-y: auto; /* 内容溢出时显示垂直滚动条 */
        }
    </style>
</head>
<body>
<div class="page" style="height: 100%;">
    {% include "/shared/_common_js.peb.html" %}

    {% include "/home/<USER>" %}

    <div class="page-wrapper">
        <div class="row g-0 justify-content-between"  style="height: 100%;margin-bottom: 5px;">
            <div class="col-1"  style="width: 15rem;">
                    {% include "/home/<USER>" %}
            </div>

            <div id="mainView" class="col d-flex flex-md-column">
                <div class="row g-0">
                    <div class="col" style="border-bottom: solid 1px lightgray">
                        <ul id="tabItemViews" class="nav nav-pills ms-2 me-2">
                            <li class="tab-switcher nav-item" id="tab_item_dashboard" data-tab-id="dashboard">
                                <div  class="tab-switcher nav-link ps-2 pe-2 active" aria-current="page" href="#">
                                    <span>{{ "首页"|trans }}</span></div>
                            </li>
                        </ul>
                    </div>
{#                    <div class=" col col-md-auto">#}
{#                        <ul class="navbar-nav ms-4 me-4">#}
{#                            <li class="nav-item dropdown">#}
{#                                <a class="nav-link dropdown-toggle" href="#navbar-third" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false">#}
{#                                    <span class="nav-link-title"> Third </span>#}
{#                                </a>#}
{#                                <div class="dropdown-menu">#}
{#                                    <a class="tab-switcher dropdown-item" href="./#"> First </a>#}
{#                                    <a class="tab-switcher dropdown-item" href="./#"> Second </a>#}
{#                                    <a class="tab-switcher dropdown-item" href="./#"> Third </a>#}
{#                                </div>#}
{#                            </li>#}
{#                        </ul>#}
{#                    </div>#}
                </div>
                <div class="row g-0 flex-fill">
                    <div id="tabContentViews" class="col" style="">
                        <iframe id="tab_content_view_dashboard" class="contentView" src="{{ href("/home/<USER>") }}" height="100%", width="100%"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(() => {
        // const tabs = [];
        // const tabContentViews = {};

        const tabItemContainer = $('#tabItemViews')
        const tabContentContainer = $('#tabContentViews')


        function onMouseMiddleKeyDown(e) {
            console.log('中键点击:', $(e.target)); // 获取被点击元素
            if (e.which === 2) {
                e.stopPropagation();
                e.preventDefault(); // 阻止默认行为（如新标签页）
                console.log('中键点击:', $(e.target)); // 获取被点击元素
                let tabItem = $(e.target);

                if(tabItem.prop('tagName') !== 'LI') {
                    tabItem = tabItem.closest('li.tab-switcher')
                }
                let tabId = tabItem.data('tab-id');
                console.log("moddle mousedown: clickedTab=", tabId);
                closeTab(tabId);
            }
        }

        function onTabClick(e) {
            e.stopPropagation();
            let clickedTab = $(e.target);
            if(clickedTab.prop('tagName') !== 'LI') {
                clickedTab = clickedTab.closest('li.tab-switcher')
            }
            console.log('tab-switcher: ', clickedTab)
            switchTab(clickedTab.data('tab-id'));
        }

        function addNavTab(menuItemData) {
            console.log('target', menuItemData);
            console.log('menuItemData', menuItemData);

            let {menuId, title, url} = menuItemData;
            console.log('menuItemData2', menuId, title, url);
            const closeIcon = $(`<i class="tab-close-icon ti ti-x ms-1"></i>`);


            const tabHtml = `
            <li id="tab_item_${menuId}" data-tab-id="${menuId}" class="tab-switcher nav-item">
                <div class="tab-switcher nav-link ps-2 pe-2" href="#">
                    <span>${title}</span>
                </div>
            </li>
            `;

            let tabItem = $(tabHtml);
            tabItem.children('div.tab-switcher').append(closeIcon);

            tabItem.click(onTabClick);

            tabItem.mousedown(onMouseMiddleKeyDown);

            tabItemContainer.append(tabItem);
            let $closeIcon = $(`#tab_item_${menuId}`).find('i.tab-close-icon');
            console.log('$closeIcon', $closeIcon);

            $closeIcon.click(() => {
                closeTab(menuId);
            });
            let tabContentViewHTML = `
                <iframe id="tab_content_view_${menuId}" class="contentView" src="${url}" height="100%", width="100%"></iframe>
            `;
            tabContentContainer.append(tabContentViewHTML);

            return menuId;
        }

        function switchTab(tabId) {
            let clickedTab = $(`#tab_item_${tabId}`);
            console.log("tab-switcher: ", tabId, clickedTab);

            $('div.tab-switcher').removeClass('active');
            clickedTab.find('div.tab-switcher').addClass('active');

            $('iframe.contentView').removeClass('d-block').addClass('d-none');
            $(`#tab_content_view_${tabId}`).removeClass('d-none').addClass('d-block');
        }

        function closeTab(tabId) {
            if(tabId === 'dashboard') {
                console.log("永不关闭首页");
                return;
            }

            $(`#tab_content_view_${tabId}`).remove();

            let $closeTab = $(`#tab_item_${tabId}`);
            if(!$closeTab.find('div.tab-switcher').hasClass('active')) {
                $closeTab.remove();
                return; // 当前标签不是高亮标签，不用自动切换到其他标签
            }

            let $nextTab = $closeTab.next('li.tab-switcher');
            if($nextTab.length > 0) {
                switchTab($nextTab.data('tab-id'));
                $closeTab.remove();
                return;
            }

            let $prevTab = $closeTab.prev('li.tab-switcher');
            if($prevTab.length > 0) {
                switchTab($prevTab.data('tab-id'));
                $closeTab.remove();
                return;
            }
        }

        $('.system-menu-item').click((e) => {
            e.stopPropagation();
            e.preventDefault();
            let menuItem = $(e.target);
            if(!menuItem.hasClass('system-menu-item')) {
                menuItem = menuItem.closest('.system-menu-item')
            }
            let menuData = menuItem.data();
            console.log('.system-menu-item.click', menuItem, menuData);
            let tabItem = $(`#tab_item_${menuData.menuId}`);
            if(tabItem.length > 0) {
                switchTab(menuData.menuId);
            } else {
                let newTabId = addNavTab(menuData);
                switchTab(newTabId);
            }

            $('.system-menu-item').removeClass('active');
            menuItem.addClass('active');
        });

        $('li.tab-switcher').click(onTabClick);

        $('li.tab-switcher').mousedown(onMouseMiddleKeyDown);

        $('i.tab-close-icon').click((e) => {
            e.stopPropagation();
            let clickedTab = $(e.target).closest('li.tab-switcher');
            let tabId = clickedTab.data('tab-id');
            console.log("tab-close-icon: tabId=", tabId);
            closeTab(tabId);
        });

    })
</script>
</body>
</html>

