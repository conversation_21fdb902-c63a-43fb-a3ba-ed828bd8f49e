<!doctype html>
<html lang="en" data-bs-theme-radius="0" data-bs-theme-base="slate" data-bs-theme-primary="teal">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>HOME</title>
    <!-- BEGIN PAGE LEVEL STYLES -->
    <link href="./libs/jsvectormap/dist/jsvectormap.css?1746001501" rel="stylesheet" />
    <!-- END PAGE LEVEL STYLES -->
    {% include "/shared/_common_css.peb.html" %}
    <style>
        /*html, body {*/
        /*    margin: 0;*/
        /*    padding: 0;*/
        /*    height: 100%;*/
        /*}*/
        aside {
            height: calc(100vh - 64px); /* 100% 视口高度 - header 高度*/
            overflow-y: auto; /* 内容溢出时显示垂直滚动条 */
        }
    </style>
</head>
<body>
<div id="unifiedModalContainer"></div>

<div class="page parent-page" style="height: 100%;">
    {% include "/shared/_common_js.peb.html" %}

    {% include "/home/<USER>" %}

    <div class="page-wrapper">
        <div class="row g-0 justify-content-between"  style="height: 100%;margin-bottom: 5px;">
            <div class="col-1"  style="width: 15rem;">
                    {% include "/home/<USER>" %}
            </div>

            <div id="mainView" class="col d-flex flex-md-column">

                <div class="row g-0 flex-fill">
                    <div class="card">
                        <div class="card-header">
                            <div class="container-fluid g-0">
                            <div class="row">
                                <div class="col">
                                    <ul id="tabItemContainer" class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
                                        <li id="tab_item_dashboard" class="tab-switcher nav-item" role="presentation" data-tab-id="dashboard">
                                            <div href="#tab_content_view_dashboard" class="tab-switcher nav-link active py-1" data-bs-toggle="tab"role="tab">
                                                {{ "首页"|trans }}
                                            </div>
                                        </li>
                                    </ul>

                                </div>
                                <div class="col col-auto">
                                    <ul id="tabItemContainer" class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
                                        <li class="nav-item dropdown  ms-auto">
                                            <div class="nav-link dropdown-toggle py-1" data-bs-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                                    <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                                                    <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"></path></svg>
                                                Dropdown
                                            </div>
                                            <div class="dropdown-menu" style="">
                                                <div class="dropdown-item" href="#"> Action </div>
                                                <div class="dropdown-item" href="#"> Another action </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            </div>
                        </div>
                        <div class="card-body p-1 d-flex">
                            <div id="tabContentContainer" class="tab-content d-flex flex-fill">
                                <div id="tab_content_view_dashboard" class="contentView tab-pane active show flex-fill" role="tabpanel" style="">
                                    <iframe src="{{ href("/home/<USER>") }}" height="100%"  width="100%"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(() => {
        // const tabs = [];
        // const tabContentViews = {};

        const tabItemContainer = $('#tabItemContainer')
        const tabContentContainer = $('#tabContentContainer')


        function onMouseMiddleKeyDown(e) {
            console.log('中键点击:', $(e.target)); // 获取被点击元素
            if (e.which === 2) {
                e.stopPropagation();
                e.preventDefault(); // 阻止默认行为（如新标签页）
                console.log('中键点击:', $(e.target)); // 获取被点击元素
                let tabItem = $(e.target);

                if(tabItem.prop('tagName') !== 'LI') {
                    tabItem = tabItem.closest('li.tab-switcher')
                }
                let tabId = tabItem.data('tab-id');
                console.log("moddle mousedown: clickedTab=", tabId);
                closeTab(tabId);
            }
        }

        function onTabClick(e) {
            e.stopPropagation();
            let clickedTab = $(e.target);
            if(clickedTab.prop('tagName') === 'I' && clickedTab.hasClass('tab-close-icon')) {
                clickedTab = clickedTab.closest('li.tab-switcher')
                let tabId = clickedTab.data('tab-id');
                console.log("tab-close-icon: tabId=", tabId);
                closeTab(tabId);
                return;
            }

            if(clickedTab.prop('tagName') !== 'LI') {
                clickedTab = clickedTab.closest('li.tab-switcher')
            }
            console.log('tab-switcher: ', clickedTab)
            switchTab(clickedTab.data('tab-id'));
        }

        function addNavTab(menuItemData) {
            console.log('target', menuItemData);
            console.log('menuItemData', menuItemData);

            let {menuId, title, url} = menuItemData;
            console.log('menuItemData2', menuId, title, url);

            const tabHtml = `
            <li id="tab_item_${menuId}" data-tab-id="${menuId}" class="tab-switcher nav-item" role="presentation">
                <div href="#tab_content_view_${menuId}" class="tab-switcher nav-link px-2 py-1" data-bs-toggle="tab" role="tab">
                    ${title}
                    <i class="tab-close-icon ti ti-x ms-2"></i>
                </div>
            </li>
            `;

            let tabItem = $(tabHtml);

            tabItem.click(onTabClick);
            tabItem.mousedown(onMouseMiddleKeyDown);
            tabItemContainer.append(tabItem);

            let tabContentViewHTML = `
            <div id="tab_content_view_${menuId}" class="contentView tab-pane flex-fill"  role="tabpanel" style="">
                <iframe src="${url}" height="100%" width="100%"></iframe>
            </div>
            `;
            tabContentContainer.append(tabContentViewHTML);

            return menuId;
        }

        function switchTab(tabId) {
            let clickedTab = $(`#tab_item_${tabId}`);
            console.log("tab-switcher: ", tabId, clickedTab);

            $('div.tab-switcher').removeClass('active');
            clickedTab.find('div.tab-switcher').addClass('active');

            $('div.contentView').removeClass('active', 'show');
            $(`#tab_content_view_${tabId}`).addClass('active', 'show');
        }

        function closeTab(tabId) {
            if(tabId === 'dashboard') {
                // console.log("永不关闭首页");
                return;
            }

            $(`#tab_content_view_${tabId}`).remove();

            let $closeTab = $(`#tab_item_${tabId}`);
            if(!$closeTab.find('div.tab-switcher').hasClass('active')) {
                $closeTab.remove();
                return; // 当前标签不是高亮标签，不用自动切换到其他标签
            }

            let $nextTab = $closeTab.next('li.tab-switcher');
            if($nextTab.length > 0) {
                switchTab($nextTab.data('tab-id'));
                $closeTab.remove();
                return;
            }

            let $prevTab = $closeTab.prev('li.tab-switcher');
            if($prevTab.length > 0) {
                switchTab($prevTab.data('tab-id'));
                $closeTab.remove();
                return;
            }
        }

        $('.system-menu-item').click((e) => {
            e.stopPropagation();
            e.preventDefault();
            let menuItem = $(e.target);
            if(!menuItem.hasClass('system-menu-item')) {
                menuItem = menuItem.closest('.system-menu-item')
            }
            let menuData = menuItem.data();
            console.log('.system-menu-item.click', menuItem, menuData);
            let tabItem = $(`#tab_item_${menuData.menuId}`);
            if(tabItem.length > 0) {
                switchTab(menuData.menuId);
            } else {
                let newTabId = addNavTab(menuData);
                switchTab(newTabId);
            }

            $('.system-menu-item').removeClass('active');
            menuItem.addClass('active');
        });

        $('li.tab-switcher').click(onTabClick);

        $('li.tab-switcher').mousedown(onMouseMiddleKeyDown);

        let newTabId = addNavTab({id: "5E567835-4157-94FC-BC5B-566FFCC24C43", title: "用户管理", url: "/admiz/system/mgmt/users"})
        switchTab(newTabId)
    })
</script>
</body>
</html>

