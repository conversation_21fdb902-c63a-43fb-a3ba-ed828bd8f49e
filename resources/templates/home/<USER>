<!--  BEGIN SIDEBAR  -->
<aside id="sidebar" class="navbar navbar-vertical navbar-expand-lg full-height" style="max-height: 100%;position: relative;" data-bs-theme="">
    <div class="container-fluid">
        <div class="collapse navbar-collapse" id="sidebar-menu">
            <!-- BEGIN NAVBAR MENU -->
            {% for subSystem in allSystemMenus %}
                {% set subSystemName = subSystem.key.name %}
                {% set systemMenus = subSystem.value %}
            <ul id="subsystem_menus_{{ subSystemName }}"  class="navbar-nav pt-lg-3 {{ activeSubSystem == subSystemName ? "d-flex" : "d-none" }} subsystem_menus">
                {% for menu in systemMenus %}
                    {% if menu.type.name == "MENU" %}
                        <li class="system-menu-item nav-item {{ loop.first ? "active" : "" }}"  data-menu-id="{{ menu.id }}"  data-title="{{ menu.title }}" data-target="{{ menu.target }}" data-url="{{ menu.url }}">
                            <a class="nav-link" href="#">
                                <span class="nav-link-title">
                                        {{ menu.title }}
                                </span>
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-item dropdown">
                            <a
                                class="nav-link dropdown-toggle show"
                                href="#navbar-base"
                                data-bs-toggle="dropdown"
                                data-bs-auto-close="false"
                                role="button"
                                aria-expanded="true"
                            >
                                <i class="ti ti-home"></i>
                                <span class="nav-link-title"> {{ menu.title }} </span>
                            </a>
                            <div class="dropdown-menu show">
                                <div class="dropdown-menu-columns">
                                    {% for subMenu in menu.menus %}
                                        <div class="system-menu-item nav-item dropdown-menu-column"
                                             data-menu-id="{{ subMenu.id }}" data-title="{{ subMenu.title }}" data-target="{{ subMenu.target }}" data-url="{{ subMenu.url }}">
                                            <a class="nav-link dropdown-item" href="{{ subMenu.url }}">
                                                    <span class="nav-link-title">
                                                            {{ subMenu.title }}
                                                    </span>
                                            </a>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </li>
                    {% endif %}
                {% endfor %}
            </ul>
            {% endfor %}

                <!-- END NAVBAR MENU -->
        </div>
    </div>
</aside>
<!--  END SIDEBAR  -->
