<!doctype html>
<html lang="zh_CN">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    {% set title = "系统登录" %}
    <title>{{ title|trans }}</title>
    {% include "/shared/_common_css.peb.html" %}
</head>
<body>
    {% include "/shared/_common_js.peb.html" %}
<div class="page page-center">
    <div class="container container-tight py-4 ">
        <div class="text-center mb-4"></div>
        <div class="card card-md">
            <div class="card-body">
                <h2 class="h2 text-center mb-4">{{ "登录您的帐号" }}</h2>
                {% include "/login/_login_form.peb.html" %}
            </div>
            <div class="hr-text">{{ "或者"|trans }}</div>
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <a id="append" href="#" class="btn btn-4 w-100">
                                {{ "使用手机号登录"|trans }}
                        </a>
                    </div>
                    <div class="col">
                        <a id="append_dismiss" href="#" class="btn btn-4 w-100">
                                {{ "使用微信扫码登录"|trans }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modal1" class="modal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modal title</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Modal body text goes here.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>

<!-- BEGIN PAGE SCRIPTS -->
    {% include "/shared/ui_settings/settings_panel.peb.html" %}
<!-- END PAGE SCRIPTS -->
</body>
</html>
