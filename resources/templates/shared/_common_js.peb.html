<script>
    window.appContextPath = "{{ request.contextPath }}";

</script>
<!-- BEGIN GLOBAL THEME SCRIPT -->
{#<script src="{{ request.contextPath }}/static/tabler/js/tabler-theme.js?1746001495"></script>#}
<!-- END GLOBAL THEME SCRIPT -->
<!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
<script src="{{ request.contextPath }}/static/libs/jquery/jquery.js"></script>
<script src="{{ request.contextPath }}/static/libs/axios/axios.js"></script>
<script src="{{ request.contextPath }}/static/app/jquery-custom-utils.js"></script>
<script src="{{ request.contextPath }}/static/tabler/js/tabler.js?**********"></script>
<script src="{{ request.contextPath }}/static/libs/popper/popper.js?**********"></script>
<script src="{{ request.contextPath }}/static/libs/tempus-dominus/tempus-dominus.js?**********"></script>
<script src="{{ request.contextPath }}/static/libs/tempus-dominus/jquery-provider.js?**********"></script>
<script>
    $(() => {
        $('input.form-control-datetime').tempusDominus();
    });
</script>
<!-- 这个必须放在 static/tabler/js/tabler.js 后面 -->
<script src="{{ request.contextPath }}/static/app/jquery-custom-plugins.js" defer></script>

<!-- END GLOBAL MANDATORY SCRIPTS -->

{#    <script type="module">#}
{#        import { Toast } from '{{ request.contextPath }}/static/libs/bootstrap/js/bootstrap.esm.js'#}

{#        console.log('module', Toast);#}

{#        Array.from(document.querySelectorAll('.toast'))#}
{#                .forEach(toastNode => new Toast(toastNode))#}
{#    </script>#}
