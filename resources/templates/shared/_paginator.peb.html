<div class="card-footer d-flex align-items-center">
    <div class="dropdown ms-auto">
        <a class="btn dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <span>每页</span>
            <span id="page-size-indicator" class="ms-2 me-2">{{ page.getSize() }}</span>
            <span>条记录</span>
        </a>
        <div class="dropdown-menu" style="">
            {% if g_is_dev %}
            <div class="dropdown-item paginator-page-size" data-page-size="3">3 条/页</div>
            {% endif %}
            <div class="dropdown-item paginator-page-size" data-page-size="10">10 条/页</div>
            <div class="dropdown-item paginator-page-size" data-page-size="20">20 条/页</div>
            <div class="dropdown-item paginator-page-size" data-page-size="50">50 条/页</div>
            <div class="dropdown-item paginator-page-size" data-page-size="100">100 条/页</div>
        </div>
    </div>
    <ul class="pagination m-0">
        <li class="page-item  {{ page.hasPrevious() ? '' : 'disabled' }}">
            <button  data-page="{{ page.hasPrevious() ? page.getNumber() - 1 : 0 }}" class="page-link" tabindex="-1" >
                <i class="ti ti-chevron-left"></i>
                上一页
            </button>
        </li>
        {% if page.getTotalPages() < 20 %}
            {% for i in range(1, page.getTotalPages()) %}
                <li class="page-item {{ page.getNumber() + 1 == i ? 'active' : '' }}">
                    <button data-page="{{ i - 1 }}" class="page-link" href="#">{{ i }}</button>
                </li>
            {% endfor %}
        {% else %}
            {% for i in range(1, 9) %}
                <li class="page-item {{ page.getNumber() + 1 == i ? 'active' : '' }}">
                    <button data-page="{{ i - 1 }}" class="page-link" href="#">{{ i }}</button>
                </li>
            {% endfor %}

            <li class="page-item disabled"><a class="page-link cursor-pointer">...</a></li>

            {% for i in range(page.getTotalPages() - 9, page.getTotalPages()) %}
                <li class="page-item {{ page.getNumber() + 1 == i ? 'active' : '' }}">
                    <button data-page="{{ i - 1 }}" class="page-link" href="#">{{ i }}</button>
                </li>
            {% endfor %}
        {% endif %}

        <li class="page-item {{ page.hasNext() ? '' : 'disabled' }}">
            <button data-page="{{ page.hasNext() ? page.getNumber() + 1 : page.getNumber() }}" class="page-link">
                下一页
                <i class="ti ti-chevron-right"></i>
            </button>
        </li>
    </ul>
</div>
<script>
    $(() => {
        $('.paginator-page-size').click((e) => {
            const pageSize = $(e.target).data('page-size');
            $('#page-size-indicator').text(pageSize);
            $('#pageSize').val(pageSize);
            $('#filterForm').submit();
        });

        $('ul.pagination >> button.page-link').click((e) => {
            const page = $(e.target).data('page');
            console.log('ul.pagination > btn.link', page, $(e.target));
            $('#pageIndex').val(page);
            $('#filterForm').submit();
        });
    })
</script>
