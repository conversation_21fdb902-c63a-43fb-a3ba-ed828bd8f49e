{% extends "/layout/layout-content-embed.peb.html" %}
{% set title = "资源详情"|trans %}

{% block block_body %}
<div class="card">
    <div class="card-header">
        <div class="row w-full">
            <div class="col">
                <h3 class="card-title mb-0">资源详情</h3>
                <p class="text-secondary m-0">查看权限资源详细信息</p>
            </div>
            <div class="col-md-auto col-sm-12">
                <div class="ms-auto d-flex flex-wrap btn-list">
                    <button type="button" class="btn btn-secondary" id="btn-back">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l14 0"/>
                            <path d="M5 12l6 6"/>
                            <path d="M5 12l6 -6"/>
                        </svg>
                        返回
                    </button>
                    <button type="button" class="btn btn-primary" id="btn-edit-resource">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                            <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                            <path d="M16 5l3 3"/>
                        </svg>
                        编辑
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">资源ID</label>
                    <div class="form-control-plaintext">{{ resource.id }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">资源名称</label>
                    <div class="form-control-plaintext">{{ resource.name }}</div>
                </div>
            </div>
        </div>
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">权限编码</label>
                    <div class="form-control-plaintext">
                        <code class="text-primary">{{ resource.permission }}</code>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">资源类型</label>
                    <div class="form-control-plaintext">
                        {% if resource.resourceType.name() == "FOLDER" %}
                            <span class="badge bg-warning text-dark">目录</span>
                        {% elseif resource.resourceType.name() == "MENU" %}
                            <span class="badge bg-info">菜单</span>
                        {% elseif resource.resourceType.name() == "FUNCTION" %}
                            <span class="badge bg-success">功能</span>
                        {% else %}
                            <span class="badge bg-purple">数据</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">URL</label>
                    <div class="form-control-plaintext">
                        {% if resource.url %}
                            <code class="text-muted">{{ resource.url }}</code>
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">排序</label>
                    <div class="form-control-plaintext">{{ resource.sortNum }}</div>
                </div>
            </div>
        </div>
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">状态</label>
                    <div class="form-control-plaintext">
                        {% if resource.disabled %}
                            <span class="badge bg-danger">禁用</span>
                        {% else %}
                            <span class="badge bg-success">启用</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">是否隐藏</label>
                    <div class="form-control-plaintext">
                        {% if resource.hidden %}
                            <span class="badge bg-secondary">隐藏</span>
                        {% else %}
                            <span class="badge bg-primary">显示</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        {% if resource.parentId %}
        <div class="row g-3">
            <div class="col-md-12">
                <div class="mb-3">
                    <label class="form-label">父级资源</label>
                    <div class="form-control-plaintext">
                        <span class="text-muted">父级ID: {{ resource.parentId }}</span>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        {% if resource.remark %}
        <div class="row g-3">
            <div class="col-md-12">
                <div class="mb-3">
                    <label class="form-label">备注</label>
                    <div class="form-control-plaintext">{{ resource.remark }}</div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">创建时间</label>
                    <div class="form-control-plaintext">{{ resource.createdTime|date('yyyy-MM-dd HH:mm:ss') }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">更新时间</label>
                    <div class="form-control-plaintext">{{ resource.updatedTime|date('yyyy-MM-dd HH:mm:ss') }}</div>
                </div>
            </div>
        </div>
        
        {% if resource.creatorId %}
        <div class="row g-3">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">创建者</label>
                    <div class="form-control-plaintext">{{ resource.creatorId }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">更新者</label>
                    <div class="form-control-plaintext">{{ resource.updaterId }}</div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
$(() => {
    // 返回按钮
    $('#btn-back').click(() => {
        window.location.href = '/admiz/system/mgmt/resources';
    });

    // 编辑按钮
    $('#btn-edit-resource').click(() => {
        window.location.href = `/admiz/system/mgmt/resources/edit?resourceId={{ resource.id }}`;
    });
});
</script>
{% endblock %}
