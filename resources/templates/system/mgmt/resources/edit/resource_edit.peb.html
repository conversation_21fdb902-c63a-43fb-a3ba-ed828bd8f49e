{% extends "/layout/layout-content-embed.peb.html" %}
{% set title = "编辑资源"|trans %}

{% block block_body %}
<div class="card">
    <div class="card-header">
        <div class="row w-full">
            <div class="col">
                <h3 class="card-title mb-0">编辑资源</h3>
                <p class="text-secondary m-0">编辑权限资源：{{ resource.name }}</p>
            </div>
            <div class="col-md-auto col-sm-12">
                <div class="ms-auto d-flex flex-wrap btn-list">
                    <button type="button" class="btn btn-secondary" id="btn-back">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l14 0"/>
                            <path d="M5 12l6 6"/>
                            <path d="M5 12l6 -6"/>
                        </svg>
                        返回
                    </button>
                    <button type="button" class="btn btn-primary" id="btn-save-resource">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l5 5l10 -10"/>
                        </svg>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div id="errors"></div>
        <div class="toast-container p-3 top-0 start-50 translate-middle-x" id="toastPlacement"></div>
        
        <form id="resourceForm">
            <input type="hidden" name="id" value="{{ resource.id }}">
            
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="resourceName" class="form-label">资源名称 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="resourceName" name="name" value="{{ resource.name }}" required>
                    <div class="invalid-feedback"></div>
                    <small class="form-hint">资源的显示名称，如"用户管理"</small>
                </div>
                <div class="col-md-6">
                    <label for="resourcePermission" class="form-label">权限编码 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="resourcePermission" name="permission" value="{{ resource.permission }}" required>
                    <div class="invalid-feedback"></div>
                    <small class="form-hint">权限编码，如"system:user:view"</small>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <div class="col-md-6">
                    <label for="resourceType" class="form-label">资源类型 <span class="text-danger">*</span></label>
                    <select class="form-select" id="resourceType" name="resourceType" required>
                        <option value="">请选择资源类型</option>
                        <option value="FOLDER" {% if resource.resourceType.name() == "FOLDER" %}selected{% endif %}>目录</option>
                        <option value="MENU" {% if resource.resourceType.name() == "MENU" %}selected{% endif %}>菜单</option>
                        <option value="FUNCTION" {% if resource.resourceType.name() == "FUNCTION" %}selected{% endif %}>功能</option>
                        <option value="DATA" {% if resource.resourceType.name() == "DATA" %}selected{% endif %}>数据</option>
                    </select>
                    <div class="invalid-feedback"></div>
                    <small class="form-hint">
                        目录：用于组织其他资源<br>
                        菜单：可访问的页面<br>
                        功能：页面内的操作按钮<br>
                        数据：数据访问权限
                    </small>
                </div>
                <div class="col-md-6">
                    <label for="resourceParentId" class="form-label">父级资源</label>
                    <select class="form-select" id="resourceParentId" name="parentId">
                        <option value="">无父级（顶级资源）</option>
                        {% for parent in parentResources %}
                        <option value="{{ parent.id }}" {% if resource.parentId == parent.id %}selected{% endif %}>
                            {{ parent.name }} ({{ parent.permission }})
                        </option>
                        {% endfor %}
                    </select>
                    <small class="form-hint">选择父级资源以构建层级结构</small>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <div class="col-md-6">
                    <label for="resourceUrl" class="form-label">URL</label>
                    <input type="text" class="form-control" id="resourceUrl" name="url" value="{{ resource.url }}" placeholder="/system/mgmt/users">
                    <small class="form-hint">资源对应的URL路径（菜单类型必填）</small>
                </div>
                <div class="col-md-6">
                    <label for="resourceSortNum" class="form-label">排序</label>
                    <input type="number" class="form-control" id="resourceSortNum" name="sortNum" value="{{ resource.sortNum }}">
                    <small class="form-hint">数值越小排序越靠前</small>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <div class="col-md-6">
                    <label for="resourceDisabled" class="form-label">状态</label>
                    <select class="form-select" id="resourceDisabled" name="disabled">
                        <option value="false" {% if not resource.disabled %}selected{% endif %}>启用</option>
                        <option value="true" {% if resource.disabled %}selected{% endif %}>禁用</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="resourceHidden" class="form-label">是否隐藏</label>
                    <select class="form-select" id="resourceHidden" name="hidden">
                        <option value="false" {% if not resource.hidden %}selected{% endif %}>显示</option>
                        <option value="true" {% if resource.hidden %}selected{% endif %}>隐藏</option>
                    </select>
                    <small class="form-hint">隐藏的资源不会在菜单中显示</small>
                </div>
            </div>
            
            <div class="mt-3">
                <label for="resourceRemark" class="form-label">备注</label>
                <textarea class="form-control" id="resourceRemark" name="remark" rows="3" placeholder="资源描述或备注信息">{{ resource.remark }}</textarea>
            </div>
        </form>
    </div>
</div>

<script>
$(() => {
    // 返回按钮
    $('#btn-back').click(() => {
        window.location.href = '/admiz/system/mgmt/resources';
    });

    // 保存资源
    $('#btn-save-resource').click(async () => {
        let $errors = $("#toastPlacement");
        
        try {
            const res = await $.axios.put('/admiz/api/system/mgmt/resources/{{ resource.id }}', document.querySelector('#resourceForm'));
            const {data} = res;
            
            if (data.succeed) {
                $errors.showToast("资源更新成功", 'success').autoDismiss();
                setTimeout(() => {
                    window.location.href = '/admiz/system/mgmt/resources';
                }, 1500);
                return;
            }

            $errors.empty().stop();

            if (data.error && typeof data.error === 'object') {
                for (let key in data.error) {
                    let $input = $(`input[name=${key}], select[name=${key}], textarea[name=${key}]`);
                    $input.siblings('.invalid-feedback').text(data.error[key]);
                    $input.addClass('is-invalid')
                        .delay(5000)
                        .queue(function(next) {
                            $input.removeClass('is-invalid');
                            next();
                        });
                }
                return;
            }

            let msg = data.msg || "更新失败，请重试！";
            $errors.showToast(msg, 'danger').autoDismiss();
        } catch (e) {
            console.error('更新资源失败:', e);
            $errors.showToast("更新失败，请稍后重试", 'danger').autoDismiss();
        }
    });

    // 权限编码实时验证
    $('#resourcePermission').on('input', function() {
        let permission = $(this).val().trim();
        let excludeId = {{ resource.id }};
        
        if (permission) {
            checkPermissionUnique(permission, excludeId);
        }
    });

    // 资源类型变化时的处理
    $('#resourceType').change(function() {
        let type = $(this).val();
        let $urlField = $('#resourceUrl');
        
        if (type === 'MENU') {
            $urlField.attr('required', true);
            $urlField.siblings('.form-hint').text('菜单类型必须填写URL路径');
        } else {
            $urlField.removeAttr('required');
            $urlField.siblings('.form-hint').text('资源对应的URL路径（菜单类型必填）');
        }
    });

    // 初始化时检查菜单类型
    $('#resourceType').trigger('change');
});

// 检查权限编码唯一性
function checkPermissionUnique(permission, excludeId) {
    let url = `/admiz/api/system/mgmt/resources/check-permission?permission=${encodeURIComponent(permission)}`;
    if (excludeId) {
        url += `&excludeId=${excludeId}`;
    }
    
    $.get(url)
        .done((result) => {
            let permissionInput = $('#resourcePermission');
            if (result.succeed && result.data === false) {
                permissionInput.addClass('is-invalid');
                permissionInput.siblings('.invalid-feedback').text('权限编码已存在');
            } else {
                permissionInput.removeClass('is-invalid');
            }
        });
}
</script>
{% endblock %}
