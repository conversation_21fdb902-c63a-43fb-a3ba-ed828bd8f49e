<!-- 资源管理相关的组件和样式 -->
<style>
.table-sort {
    background: none;
    border: none;
    color: inherit;
    font-weight: inherit;
    text-align: left;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.table-sort:hover {
    color: var(--tblr-primary);
}

.btn-list .btn {
    margin-right: 0.25rem;
}

.btn-list .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75em;
}

.text-truncate {
    max-width: 200px;
}

.resource-tree {
    max-height: 400px;
    overflow-y: auto;
}

.resource-type-icon {
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-right: 8px;
}
</style>

<!-- 资源创建/编辑模态框 -->
<div class="modal fade" id="resourceFormModal" tabindex="-1" aria-labelledby="resourceFormModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resourceFormModalTitle">创建资源</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="resourceForm">
                    <input type="hidden" id="resourceId" name="id">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="resourceName" class="form-label">资源名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="resourceName" name="name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="resourcePermission" class="form-label">权限编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="resourcePermission" name="permission" required>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="resourceType" class="form-label">资源类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="resourceType" name="resourceType" required>
                                <option value="">请选择资源类型</option>
                                <option value="FOLDER">目录</option>
                                <option value="MENU">菜单</option>
                                <option value="FUNCTION">功能</option>
                                <option value="DATA">数据</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="resourceParentId" class="form-label">父级资源</label>
                            <select class="form-select" id="resourceParentId" name="parentId">
                                <option value="">无父级（顶级资源）</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="resourceUrl" class="form-label">URL</label>
                            <input type="text" class="form-control" id="resourceUrl" name="url" placeholder="/path/to/resource">
                        </div>
                        <div class="col-md-6">
                            <label for="resourceSortNum" class="form-label">排序</label>
                            <input type="number" class="form-control" id="resourceSortNum" name="sortNum" value="100">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="resourceDisabled" class="form-label">状态</label>
                            <select class="form-select" id="resourceDisabled" name="disabled">
                                <option value="false">启用</option>
                                <option value="true">禁用</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="resourceHidden" class="form-label">是否隐藏</label>
                            <select class="form-select" id="resourceHidden" name="hidden">
                                <option value="false">显示</option>
                                <option value="true">隐藏</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="resourceRemark" class="form-label">备注</label>
                        <textarea class="form-control" id="resourceRemark" name="remark" rows="3" placeholder="资源描述或备注信息"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btn-save-resource">保存</button>
            </div>
        </div>
    </div>
</div>
