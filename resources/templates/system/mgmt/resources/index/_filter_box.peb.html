<div class="card">
    <div class="card-stamp">
        <div class="card-stamp-icon bg-white text-primary"><i class="ti ti-shield-check"></i></div>
    </div>
    <div class="card-body p-2">
        <div class="container-fluid">
            <form method="get" action="/admiz/system/mgmt/resources" class="row m-1" id="filterForm">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text">搜索</span>
                        <input type="text" class="form-control" name="searchText" 
                               value="{{ resourceIndexVo.searchText }}" 
                               placeholder="资源名称、权限编码或URL" autocomplete="off">
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">类型</span>
                        <select name="resourceType" class="form-select">
                            <option value="">全部类型</option>
                            <option value="FOLDER" {% if resourceIndexVo.resourceType == "FOLDER" %}selected{% endif %}>目录</option>
                            <option value="MENU" {% if resourceIndexVo.resourceType == "MENU" %}selected{% endif %}>菜单</option>
                            <option value="FUNCTION" {% if resourceIndexVo.resourceType == "FUNCTION" %}selected{% endif %}>功能</option>
                            <option value="DATA" {% if resourceIndexVo.resourceType == "DATA" %}selected{% endif %}>数据</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">状态</span>
                        <select name="disabled" class="form-select">
                            <option value="">全部状态</option>
                            <option value="false" {% if resourceIndexVo.disabled == "false" %}selected{% endif %}>启用</option>
                            <option value="true" {% if resourceIndexVo.disabled == "true" %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <button id="btnSubmit" type="submit" class="btn btn-primary btn-4">
                        <i class="ti ti-search"></i> 搜索
                    </button>
                </div>
                <input type="hidden" name="page" id="pageIndex" value="{{ page.getNumber() }}" />
                <input type="hidden" name="size" id="pageSize" value="{{ page.getSize() }}" />
                <input type="hidden" name="sort" id="sort" value="{{ sort.formValue }}" />
                <input type="hidden" name="rnd" id="rnd" value="{{ random() }}" />
            </form>
        </div>
    </div>
</div>
