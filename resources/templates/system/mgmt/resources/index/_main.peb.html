<table class="table table-vcenter card-table">
    <thead>
    <tr>
        <th class="w-1">
            <input id="chkCheckAll" class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select all resources">
        </th>
        <th class="w-1">ID</th>
        <th>资源名称</th>
        <th>权限编码</th>
        <th>资源类型</th>
        <th>URL</th>
        <th>状态</th>
        <th>排序</th>
        <th class="w-1">操作</th>
    </tr>
    </thead>
    <tbody>
    {% for resource in page.content %}
    <tr>
        <td>
            <input name="id" class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select resource" value="{{ resource.id }}">
        </td>
        <td>
            <span class="text-secondary">{{ resource.id }}</span>
        </td>
        <td>
            <div class="d-flex py-1 align-items-center">
                {% if resource.resourceType.name() == "FOLDER" %}
                    <span class="avatar avatar-sm me-2" style="background-color: #ffc107;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"/>
                        </svg>
                    </span>
                {% elseif resource.resourceType.name() == "MENU" %}
                    <span class="avatar avatar-sm me-2" style="background-color: #17a2b8;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <line x1="4" y1="6" x2="20" y2="6"/>
                            <line x1="4" y1="12" x2="20" y2="12"/>
                            <line x1="4" y1="18" x2="16" y2="18"/>
                        </svg>
                    </span>
                {% elseif resource.resourceType.name() == "FUNCTION" %}
                    <span class="avatar avatar-sm me-2" style="background-color: #28a745;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <rect x="3" y="3" width="18" height="18" rx="2"/>
                            <path d="M9 9h6v6h-6z"/>
                        </svg>
                    </span>
                {% else %}
                    <span class="avatar avatar-sm me-2" style="background-color: #6f42c1;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"/>
                            <rect x="9" y="3" width="6" height="4" rx="2"/>
                        </svg>
                    </span>
                {% endif %}
                <div class="flex-fill">
                    <div class="font-weight-medium">{{ resource.name }}</div>
                    {% if resource.parentId %}
                        <div class="text-secondary">父级ID: {{ resource.parentId }}</div>
                    {% endif %}
                </div>
            </div>
        </td>
        <td>
            <code class="text-primary">{{ resource.permission }}</code>
        </td>
        <td>
            {% if resource.resourceType.name() == "FOLDER" %}
                <span class="badge bg-warning text-dark">目录</span>
            {% elseif resource.resourceType.name() == "MENU" %}
                <span class="badge bg-info">菜单</span>
            {% elseif resource.resourceType.name() == "FUNCTION" %}
                <span class="badge bg-success">功能</span>
            {% else %}
                <span class="badge bg-purple">数据</span>
            {% endif %}
        </td>
        <td>
            {% if resource.url %}
                <code class="text-muted">{{ resource.url }}</code>
            {% else %}
                <span class="text-muted">-</span>
            {% endif %}
        </td>
        <td>
            {% if resource.disabled %}
                <span class="badge bg-danger">禁用</span>
            {% else %}
                <span class="badge bg-success">启用</span>
            {% endif %}
        </td>
        <td>
            <span class="text-secondary">{{ resource.sortNum }}</span>
        </td>
        <td>
            <div class="btn-list flex-nowrap">
                <button class="btn btn-sm btn-outline-primary btn-edit-resource" data-resource-id="{{ resource.id }}" title="编辑">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                        <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                        <path d="M16 5l3 3"/>
                    </svg>
                </button>
                <button class="btn btn-sm btn-outline-danger btn-delete-resource" data-resource-id="{{ resource.id }}" data-resource-name="{{ resource.name }}" title="删除">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <line x1="4" y1="7" x2="20" y2="7"/>
                        <line x1="10" y1="11" x2="10" y2="17"/>
                        <line x1="14" y1="11" x2="14" y2="17"/>
                        <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/>
                        <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/>
                    </svg>
                </button>
            </div>
        </td>
    </tr>
    {% endfor %}
    </tbody>
</table>

<script>
$(() => {
    // 创建资源按钮
    $('#btn-create-resource').click(() => {
        window.location.href = '/admiz/system/mgmt/resources/create';
    });

    // 编辑资源按钮
    $('.btn-edit-resource').click((e) => {
        let resourceId = $(e.currentTarget).data('resource-id');
        window.location.href = `/admiz/system/mgmt/resources/edit?resourceId=${resourceId}`;
    });

    // 删除资源按钮
    $('.btn-delete-resource').click((e) => {
        let resourceId = $(e.currentTarget).data('resource-id');
        let resourceName = $(e.currentTarget).data('resource-name');

        if (confirm(`确定要删除资源 "${resourceName}" 吗？此操作不可恢复。`)) {
            deleteResource(resourceId);
        }
    });

    // 全选/取消全选
    $('#chkCheckAll').click((e) => {
        let $chkCheckAll = $(e.target);
        let isCheckAll = $chkCheckAll.is(':checked');
        $('input[name=id].form-check-input').prop('checked', isCheckAll);
    });

    // 批量操作功能
    $('.dropdown-menu .dropdown-item').click(function(e) {
        e.preventDefault();
        let action = $(this).text().trim();
        let selectedIds = getSelectedResourceIds();

        if (selectedIds.length === 0) {
            alert('请先选择要操作的资源');
            return;
        }

        switch(action) {
            case '启用':
                batchUpdateResourceStatus(selectedIds, false);
                break;
            case '禁用':
                batchUpdateResourceStatus(selectedIds, true);
                break;
            case '删除':
                batchDeleteResources(selectedIds);
                break;
            case '导出':
                exportResources(selectedIds);
                break;
        }
    });
});

// 获取选中的资源ID
function getSelectedResourceIds() {
    let selectedIds = [];
    $('input[name=id].form-check-input:checked').each(function() {
        selectedIds.push($(this).val());
    });
    return selectedIds;
}

// 删除资源
function deleteResource(resourceId) {
    $.ajax({
        url: `/admiz/api/system/mgmt/resources/${resourceId}`,
        method: 'DELETE'
    })
    .done((result) => {
        if (result.succeed) {
            alert(result.msg || '删除成功');
            location.reload();
        } else {
            alert('删除失败: ' + result.msg);
        }
    })
    .fail((xhr) => {
        alert('删除失败，请稍后重试');
    });
}

// 批量更新资源状态
function batchUpdateResourceStatus(resourceIds, disabled) {
    let action = disabled ? '禁用' : '启用';

    if (confirm(`确定要${action}选中的 ${resourceIds.length} 个资源吗？`)) {
        $.ajax({
            url: '/admiz/api/system/mgmt/resources/batch-status',
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                resourceIds: resourceIds,
                disabled: disabled
            })
        })
        .done((result) => {
            if (result.succeed) {
                alert(result.msg || `批量${action}成功`);
                location.reload();
            } else {
                alert(`批量${action}失败: ` + result.msg);
            }
        })
        .fail((xhr) => {
            alert(`批量${action}失败，请稍后重试`);
        });
    }
}

// 批量删除资源
function batchDeleteResources(resourceIds) {
    if (confirm(`确定要删除选中的 ${resourceIds.length} 个资源吗？此操作不可恢复。`)) {
        $.ajax({
            url: '/admiz/api/system/mgmt/resources/batch-delete',
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify({
                resourceIds: resourceIds
            })
        })
        .done((result) => {
            if (result.succeed) {
                alert(result.msg || '批量删除成功');
                location.reload();
            } else {
                alert('批量删除失败: ' + result.msg);
            }
        })
        .fail((xhr) => {
            alert('批量删除失败，请稍后重试');
        });
    }
}

// 导出资源
function exportResources(resourceIds) {
    let url = '/admiz/api/system/mgmt/resources/export';
    if (resourceIds.length > 0) {
        url += '?ids=' + resourceIds.join(',');
    }
    window.open(url, '_blank');
}
</script>
