{% extends "/layout/layout-content-embed.peb.html" %}
{% set title = "资源管理"|trans %}

{% block block_body %}
{% include "./_components.peb.html" %}
{% include "./_filter_box.peb.html" %}

<div class="card mt-2 mb-2">
    <div class="card-table">
        <div class="card-header p-2">
            <div class="row w-full">
                <div class="col">
                    <h3 class="card-title mb-0">资源管理</h3>
                    <p class="text-secondary m-0">管理系统权限资源</p>
                </div>
                <div class="col-md-auto col-sm-12">
                    <div class="ms-auto d-flex flex-wrap btn-list">
                        <button type="button" class="btn btn-primary" id="btn-create-resource">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            创建资源
                        </button>
                        <div class="dropdown">
                            <button class="btn dropdown-toggle" data-bs-toggle="dropdown">批量操作</button>
                            <div class="dropdown-menu">
                                <a href="#" class="dropdown-item">启用</a>
                                <a href="#" class="dropdown-item">禁用</a>
                                <a href="#" class="dropdown-item">删除</a>
                                <a href="#" class="dropdown-item">导出</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% include "./_main.peb.html" %}
    </div>
</div>

{% include "/shared/_paginator.peb.html" %}
{% endblock %}
