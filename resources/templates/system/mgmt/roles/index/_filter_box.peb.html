<div class="card">
    <div class="card-body">
        <form method="get" action="/admiz/system/mgmt/roles">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">搜索</label>
                        <input type="text" class="form-control" name="searchText" 
                               value="{{ roleIndexVo.searchText }}" 
                               placeholder="角色名称、显示名称或备注">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="disabled">
                            <option value="">全部</option>
                            <option value="false" {% if roleIndexVo.disabled == "false" %}selected{% endif %}>启用</option>
                            <option value="true" {% if roleIndexVo.disabled == "true" %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">排序</label>
                        <select class="form-select" name="sort">
                            <option value="">默认排序</option>
                            <option value="name,asc" {% if roleIndexVo.sort == "name,asc" %}selected{% endif %}>角色名称 ↑</option>
                            <option value="name,desc" {% if roleIndexVo.sort == "name,desc" %}selected{% endif %}>角色名称 ↓</option>
                            <option value="sortNum,asc" {% if roleIndexVo.sort == "sortNum,asc" %}selected{% endif %}>排序号 ↑</option>
                            <option value="sortNum,desc" {% if roleIndexVo.sort == "sortNum,desc" %}selected{% endif %}>排序号 ↓</option>
                            <option value="createTime,asc" {% if roleIndexVo.sort == "createTime,asc" %}selected{% endif %}>创建时间 ↑</option>
                            <option value="createTime,desc" {% if roleIndexVo.sort == "createTime,desc" %}selected{% endif %}>创建时间 ↓</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="10" cy="10" r="7"/>
                                    <path d="M21 21l-6 -6"/>
                                </svg>
                                搜索
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
