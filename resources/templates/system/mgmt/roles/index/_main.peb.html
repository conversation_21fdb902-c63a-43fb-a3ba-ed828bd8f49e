<table class="table table-vcenter card-table">
    <thead>
    <tr>
        <th class="w-1">
            <input id="chkCheckAll" class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select all roles">
        </th>
        <th class="w-1">ID</th>
        <th>角色名称</th>
        <th>显示名称</th>
        <th>状态</th>
        <th>创建时间</th>
        <th>备注</th>
        <th class="w-1">操作</th>
    </tr>
    </thead>
    <tbody>
    {% for role in page.content %}
    <tr>
        <td>
            <input name="id" class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select role" value="{{ role.id }}">
        </td>
        <td>
            <span class="text-secondary">{{ role.id }}</span>
        </td>
        <td>
            <div class="d-flex py-1 align-items-center">
                <div class="flex-fill">
                    <div class="font-weight-medium">{{ role.name }}</div>
                </div>
            </div>
        </td>
        <td>
            <div class="text-secondary">{{ role.displayName }}</div>
        </td>
        <td>
            {% if role.disabled %}
                <span class="badge bg-danger me-1"></span>
                禁用
            {% else %}
                <span class="badge bg-success me-1"></span>
                启用
            {% endif %}
        </td>
        <td>
            <div class="text-secondary">{{ role.createTime | date("yyyy-MM-dd HH:mm") }}</div>
        </td>
        <td>
            <div class="text-secondary">
                {% if role.remark %}
                    {{ role.remark | abbreviate(30) }}
                {% else %}
                    -
                {% endif %}
            </div>
        </td>
        <td>
            <div class="btn-list flex-nowrap">
                <button class="btn btn-sm btn-outline-primary btn-edit-role" data-role-id="{{ role.id }}" title="编辑">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                        <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                        <path d="M16 5l3 3"/>
                    </svg>
                </button>
                <button class="btn btn-sm btn-outline-info btn-permissions-role" data-role-id="{{ role.id }}" title="权限管理">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <rect x="5" y="11" width="14" height="10" rx="2"/>
                        <circle cx="12" cy="16" r="1"/>
                        <path d="M8 11v-4a4 4 0 0 1 8 0v4"/>
                    </svg>
                </button>
                <button class="btn btn-sm btn-outline-danger btn-delete-role" data-role-id="{{ role.id }}" data-role-name="{{ role.name }}" title="删除">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <line x1="4" y1="7" x2="20" y2="7"/>
                        <line x1="10" y1="11" x2="10" y2="17"/>
                        <line x1="14" y1="11" x2="14" y2="17"/>
                        <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/>
                        <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/>
                    </svg>
                </button>
            </div>
        </td>
    </tr>
    {% endfor %}
    </tbody>
</table>

<script>
$(() => {
    // 创建角色按钮
    $('#btn-create-role').click(() => {
        $('#roleFormModalTitle').text('创建角色');
        $('#roleForm')[0].reset();
        $('#roleId').val('');
        $('#roleFormModal').modal('show');
    });

    // 编辑角色按钮
    $('.btn-edit-role').click((e) => {
        let roleId = $(e.currentTarget).data('role-id');
        loadRoleData(roleId);
    });

    // 权限管理按钮
    $('.btn-permissions-role').click((e) => {
        let roleId = $(e.currentTarget).data('role-id');
        window.location.href = `/admiz/system/mgmt/roles/${roleId}/permissions`;
    });

    // 删除角色按钮
    $('.btn-delete-role').click((e) => {
        let roleId = $(e.currentTarget).data('role-id');
        let roleName = $(e.currentTarget).data('role-name');

        if (confirm(`确定要删除角色 "${roleName}" 吗？此操作不可恢复。`)) {
            deleteRole(roleId);
        }
    });

    // 保存角色按钮
    $('#btn-save-role').click(() => {
        saveRole();
    });

    // 角色名称实时验证
    $('#roleName').on('input', function() {
        let name = $(this).val().trim();
        let excludeId = $('#roleId').val();

        if (name) {
            checkRoleName(name, excludeId);
        }
    });

    // 批量操作功能
    $('.dropdown-menu .dropdown-item').click(function(e) {
        let action = $(this).text().trim();
        let selectedIds = getSelectedRoleIds();
        
        if (selectedIds.length === 0) {
            showToast('请先选择要操作的角色', 'warning');
            return;
        }
        
        switch(action) {
            case '启用':
                batchUpdateRoleStatus(selectedIds, false);
                break;
            case '禁用':
                batchUpdateRoleStatus(selectedIds, true);
                break;
            case '导出':
                exportRoles(selectedIds);
                break;
        }
    });


    $('#chkCheckAll').click((e) => {
        let isChecked = $(e.target).is(':checked');
        $('input[name=id].form-check-input').prop('checked', isChecked);
    });

    $('input[name=id].form-check-input').click((e) => {
        let totalCheckboxes = $('input[name=id].form-check-input').length;
        let checkedCheckboxes = $('input[name=id].form-check-input:checked').length;
        $('#chkCheckAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });
});

// 获取选中的角色ID
function getSelectedRoleIds() {
    let selectedIds = [];
    $('input[name=id].form-check-input:checked').each(function() {
        selectedIds.push(parseInt($(this).val()));
    });
    return selectedIds;
}

// 批量更新角色状态
function batchUpdateRoleStatus(roleIds, disabled) {
    let action = disabled ? '禁用' : '启用';

    if (confirm(`确定要${action}选中的 ${roleIds.length} 个角色吗？`)) {

        $.ajax({
            url: '/admiz/api/system/mgmt/roles/batch-status',
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                roleIds: roleIds,
                disabled: disabled
            })
        })
        .done((result) => {
            if (result.succeed) {
                showToast(result.msg || `批量${action}成功`, 'success');
                location.reload();
            } else {
                showToast(`批量${action}失败: ` + result.msg, 'danger');
            }
        })
        .fail((xhr) => {
            let errorMsg = `批量${action}失败，请稍后重试`;
            try {
                const response = JSON.parse(xhr.responseText);
                if (response && response.msg) {
                    errorMsg = `批量${action}失败: ` + response.msg;
                }
            } catch (e) {
                // 解析失败时使用默认错误信息
            }
            showToast(errorMsg, 'danger');
        });
    }
}

// 导出角色
function exportRoles(roleIds) {
    let url = '/admiz/api/system/mgmt/roles/export';
    if (roleIds.length > 0) {
        url += '?ids=' + roleIds.join(',');
    }
    window.open(url, '_blank');
}

// 加载角色数据
function loadRoleData(roleId) {
    $.get(`/admiz/api/system/mgmt/roles/${roleId}`)
        .done((result) => {
            if (result.succeed) {
                let role = result.data;
                $('#roleFormModalTitle').text('编辑角色');
                $('#roleId').val(role.id);
                $('#roleName').val(role.name);
                $('#roleDisplayName').val(role.displayName);
                $('#roleSortNum').val(role.sortNum);
                $('#roleDisabled').val(role.disabled.toString());
                $('#roleRemark').val(role.remark || '');
                $('#roleFormModal').modal('show');
            } else {
                showToast('加载角色数据失败: ' + result.msg, 'danger');
            }
        })
        .fail((xhr) => {
            let errorMsg = '加载角色数据失败，请稍后重试';
            try {
                const response = JSON.parse(xhr.responseText);
                if (response && response.msg) {
                    errorMsg = '加载角色数据失败: ' + response.msg;
                }
            } catch (e) {
                // 解析失败时使用默认错误信息
            }
            showToast(errorMsg, 'danger');
        });
}

// 保存角色
function saveRole() {
    let form = $('#roleForm')[0];
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }

    let roleId = $('#roleId').val();
    let isEdit = roleId && roleId.trim() !== '';
    
    let data = {
        name: $('#roleName').val().trim(),
        displayName: $('#roleDisplayName').val().trim(),
        sortNum: parseInt($('#roleSortNum').val()) || 0,
        disabled: $('#roleDisabled').val() === 'true',
        remark: $('#roleRemark').val().trim()
    };

    if (isEdit) {
        data.id = parseInt(roleId);
    }

    let url = isEdit ? `/admiz/api/system/mgmt/roles/${roleId}` : '/admiz/api/system/mgmt/roles';
    let method = isEdit ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data)
    })
    .done((result) => {
        if (result.succeed) {
            $('#roleFormModal').modal('hide');
            showToast(isEdit ? '角色更新成功' : '角色创建成功', 'success');
            location.reload();
        } else {
            showToast('保存失败: ' + result.msg, 'danger');
        }
    })
    .fail((xhr) => {
        let errorMsg = '保存失败，请稍后重试';
        try {
            const response = JSON.parse(xhr.responseText);
            if (response && response.msg) {
                errorMsg = '保存失败: ' + response.msg;
            }
        } catch (e) {
            // 解析失败时使用默认错误信息
        }
        showToast(errorMsg, 'danger');
    });
}

// 删除角色
function deleteRole(roleId) {
    $.ajax({
        url: `/admiz/api/system/mgmt/roles/${roleId}`,
        method: 'DELETE'
    })
    .done((result) => {
        if (result.succeed) {
            showToast('角色删除成功', 'success');
            location.reload();
        } else {
            showToast('删除失败: ' + result.msg, 'danger');
        }
    })
    .fail((xhr) => {
        let errorMsg = '删除失败，请稍后重试';
        try {
            const response = JSON.parse(xhr.responseText);
            if (response && response.msg) {
                errorMsg = '删除失败: ' + response.msg;
            }
        } catch (e) {
            // 解析失败时使用默认错误信息
        }
        showToast(errorMsg, 'danger');
    });
}

// 检查角色名称
function checkRoleName(name, excludeId) {
    let url = `/admiz/api/system/mgmt/roles/check-name?name=${encodeURIComponent(name)}`;
    if (excludeId) {
        url += `&excludeId=${excludeId}`;
    }
    
    $.get(url)
        .done((result) => {
            let nameInput = $('#roleName');
            if (result.succeed && result.data === false) {
                nameInput.addClass('is-invalid');
                nameInput.siblings('.invalid-feedback').text('角色名称已存在');
            } else {
                nameInput.removeClass('is-invalid');
            }
        });
}

function showToast(message, type = 'success') {
    $('#toast-container').showToast(message, type).autoDismiss();
}
</script>