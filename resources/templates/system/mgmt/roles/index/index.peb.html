{% extends "/layout/layout-content-embed.peb.html" %}
{% set title = "角色管理"|trans %}

{% block block_body %}
<div class="toast-container position-fixed top-0 start-50 translate-middle-x p-3" id="toast-container"
     style="z-index: 9999;"></div>
{% include "./_components.peb.html" %}
{% include "./_filter_box.peb.html" %}

<div class="card mt-2 mb-2">
    <div class="card-table">
        <div class="card-header p-2">
            <div class="row w-full">
                <div class="col">
                    <h3 class="card-title mb-0">角色管理</h3>
                    <p class="text-secondary m-0">管理系统角色和权限</p>
                </div>
                <div class="col-md-auto col-sm-12">
                    <div class="ms-auto d-flex flex-wrap btn-list">
                        <button type="button" class="btn btn-primary" id="btn-create-role">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            创建角色
                        </button>
                        <div class="dropdown">
                            <div class="btn dropdown-toggle" data-bs-toggle="dropdown">批量操作</div>
                            <div class="dropdown-menu">
                                <div class="dropdown-item">启用</div>
                                <div class="dropdown-item">禁用</div>
                                <div class="dropdown-item">导出</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="advanced-table">
            <div class="table-responsive">
                {% include "./_main.peb.html" %}
            </div>

            {{ paginator(page) }}
        </div>
    </div>
</div>

<!-- 角色创建/编辑模态框 -->
<div class="modal modal-blur fade" id="roleFormModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roleFormModalTitle">创建角色</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="roleForm">
                    <input type="hidden" id="roleId" name="id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">角色名称</label>
                                <input id="roleName" type="text" class="form-control " readonly value="{{ role.roleName }}">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label required">显示名称</label>
                                <input type="text" class="form-control" id="roleDisplayName" name="displayName" placeholder="请输入显示名称" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">排序号</label>
                                <input type="number" class="form-control" id="roleSortNum" name="sortNum" placeholder="请输入排序号" value="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">状态</label>
                                <select class="form-select" id="roleDisabled" name="disabled">
                                    <option value="false">启用</option>
                                    <option value="true">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" id="roleRemark" name="remark" rows="3" placeholder="请输入备注信息"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btn-save-role">保存</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

