<!doctype>
<html lang="en" data-bs-theme-radius="0" data-bs-theme-base="slate" data-bs-theme-primary="teal">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>{% block content_title %}
        {{ title | default("TITLE") }}
    {% endblock %}
    </title>
    {% include "/shared/_common_css.peb.html" %}
</head>
<body class="layout-fluid content-page">
<!-- BEGIN GLOBAL THEME SCRIPT -->
{% include "/shared/_common_js.peb.html" %}
<!--{% import "/shared/macros/_icons.peb.html" %}-->
<!--{% import "/shared/macros/_common_helpers.peb.html" %}-->
<!-- END GLOBAL THEME SCRIPT -->

<script>
    $(document).ready(() => {
        $(window.frameElement).height($('div.page-wrapper').height() + 2);
    });
</script>

{%
set accountMenus = [
    {"title": "账号信息", "url": "/system/mgmt/users/details/account"},
    {"title": "角色分配", "url": "/system/mgmt/users/details/roles"},
    {"title": "用户档案", "url": "/system/mgmt/users/details/profile"}
]
%}

{%
set otherMenus = [
    {"title": "帐号活动", "url": "/system/mgmt/users/details/activity"}
]
%}

<div class="page-wrapper">
    <div class="container-xl g-0">
        <div class="card">
            <div class="row g-0">
                <div class="col-12 col-md-3 border-end">
                    <div class="card-body">
                        <h4 class="subheader">帐号设置</h4>
                        <div class="list-group list-group-transparent">
                            {% for item in accountMenus %}
                            <a  href="{{ href(item.url) }}?userId={{ user.id }}" class="list-group-item list-group-item-action d-flex align-items-center {{ is_current_path(item.url) ? 'active' : '' }}">{{ item.title }}</a>
                            {% endfor %}
                        </div>
                        <h4 class="subheader mt-4">其他信息</h4>
                        <div class="list-group list-group-transparent">
                            {% for item in otherMenus %}
                            <a href="{{ href(item.url) }}?userId={{ user.id }}" class="list-group-item list-group-item-action d-flex align-items-center {{ is_current_path(item.url) ? 'active' : '' }}">{{ item.title }}</a>
                            {% endfor %}
                            <a href="{{ href(actualUrl) }}" 
                               class="list-group-item list-group-item-action d-flex align-items-center {{ is_current_path(actualUrl) ? 'active' : '' }}">
                                {{ item.title }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-9 d-flex flex-column">
                    {% block block_body %}

                    {% endblock %}
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>