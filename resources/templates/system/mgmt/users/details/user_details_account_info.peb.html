 {% extends "./layout-user-details.peb.html" %}
{% set title = "用户编辑"|trans %}

{% block block_body %}
<script>
$(() => {
    // $('[data-bs-toggle="popover"]').popover();
    $('#btnGeneratePassword').click(async (e) => {
        let newPassword = $.utils.generateRandomString(8);
        $('input[name=password]').val(newPassword);
        $('input[name=passwordConfirm]').val(newPassword);

        try {
            await window.navigator.clipboard.writeText(newPassword)
            let popover = await $(e.target).popover({
                title: '已生成新密码：' + newPassword,
                content: '新密码已经复制到粘贴板，你可以粘贴到其他地方',
                placement: 'right', // 位置：top, bottom, left, right
                trigger: 'focus'  // 触发方式：click, hover, focus, manual
            });

            popover.on('hide.bs.popover', () => {
                popover.popover('dispose');
            });

            popover.popover('show');
        } catch (e) {
            $(e.target).popover('show')
        }
    });

    $("#btnSaveUserAccountInfo").click(async () => {
        let $errors = $("#toastPlacement");

        try {
            const res = await $.axios.post('/system/mgmt/users/details/account/save', document.querySelector('#userAccountInfoForm'));
            const {data} = res;
            let error = data.error;
            console.log('res=', res, typeof error);
            if (data.succeed) {
                console.log('save succeed');
                return $errors.showToast("用户保存成功").autoDismiss();
            }

            $errors.empty().stop();

            if (error && typeof error === 'object') {
                for (let key in (error ?? {})) {
                    console.log(`Key: ${key}, Value: ${error[key]}`);
                    let $input = $(`input[name=${key}]`);
                    $input.siblings(`div[name="${key}"]`)
                        .text(error[key]);
                    $input.addClass('is-invalid')
                        .delay(5000)
                        .queue(function(next) {
                            $input.removeClass('is-invalid');
                            next();
                        });
                }
                $errors.autoDismiss();
                return;
            }

            let msg = data.msg ?? "登录失败，请重试！";
            return $errors.showToast(msg, data.succeed ? 'success' : 'danger').autoDismiss();
        } catch (e) {
            $errors.showToast("保存失败").autoDismiss();
        }
    })
})
</script>
<div class="card-body p-2">
    <div id="errors">
    </div>
    <div class="toast-container p-3 top-0 start-50 translate-middle-x" id="toastPlacement" data-original-class="toast-container p-3">
    </div>
    <form id="userAccountInfoForm" class="p-0">
        <div class="container-fluid">
            <div class="row g-3">
                <div class="col-3">
                    <div class="form-label">用户名</div>
                    <input name="login" type="text" class="form-control " disabled value="{{ user.login }}">
                </div>
                <div class="col-3">
                    <div class="form-label">名称</div>
                    <input name="displayName" type="text" class="form-control " value="{{ user.displayName }}">
                </div>
                <div class="col-auto">
                    <div class="form-label">性别</div>
                    <select name="sex" class="form-select">
                        <option {{ user.sex == 'MALE'    ? 'selected' : '' }} value="{{ user.sex }}">男性</option>
                        <option {{ user.sex == 'FEMALE'  ? 'selected' : '' }} value="{{ user.sex }}">女性</option>
                        <option {{ user.sex is empty     ? 'selected' : '' }} value="">未知</option>
                    </select>
                </div>
                <div class="col-auto">
                    <div class="form-label">账号状态</div>
                    <style>
                        .form-selectgroup-input.account-lock-status:checked + .form-selectgroup-label {
                            border-width: medium;
                        }
                        .form-selectgroup-input.account-lock-status.account-locked-status:checked + .form-selectgroup-label {
                            border-width: medium;
                            z-index: 1;
                            color: var(--tblr-danger);
                            background: rgba(var(--tblr-danger-rgb), 0.04);
                            border-color: var(--tblr-danger);
                        }
                    </style>
                    <div class="form-selectgroup">
                        <label class="form-selectgroup-item px-0">
                            <input type="radio" name="locked" value="false" class="form-selectgroup-input account-lock-status" {{ user.locked ? '' : 'checked' }}>
                            <span class="form-selectgroup-label  px-1 ">{{ icon('mood-check') }}正常</span>
                        </label>
                        <label class="form-selectgroup-item px-0 me-0">
                            <input type="radio" name="locked" value="true" class="form-selectgroup-input account-lock-status account-locked-status" {{ user.locked ? 'checked' : '' }}>
                            <span class="form-selectgroup-label  px-1"  data-bs-toggle="tooltip" data-bs-placement="right" data-bs-title="{{ user.lockReason }}">{{ icon('mood-off') }}锁定</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="row g-3 mt-1 {{ user.locked ? '' : 'd-none' }}">
                    <div class="col-3">
                        <div class="form-label">解封时间</div>
                        <div class="input-icon mb-2">
                            <input name="lockedUntil" type="text" class="form-control form-control-datetime"  value="{{ user.lockedUntil|date('yyyy-MM-dd HH:mm:ss') }}">
                        </div>
                    </div>
                    <div class="col-9">
                        <div class="form-label">封禁原因</div>
                        <input name="lockReason" type="text" class="form-control" value="{{ user.lockReason }}">
                    </div>
            </div>
            <div class="row g-3 mt-1">
                <div class="col-3">
                    <div class="form-label">手机号</div>
                    <input name="mobile" type="text" class="form-control" value="{{ user.mobile }}">
                </div>
                <div class="col-3">
                    <div class="form-label">邮箱</div>
                    <input name="email" type="text" class="form-control" value="{{ user.email }}">
                </div>
            </div>

            <h3 class="card-title mt-4">密码</h3>
            <p class="card-subtitle">输入两次新密码来修改密码，留空则不修改</p>
            <div>
                <div class="row g-2">
                    <div class="col-3">
                        <input name="password" type="password" class="form-control w-auto" value="" autocomplete="new-password">
                    </div>
                    <div class="col-3">
                        <input name="passwordConfirm" type="password" class="form-control w-auto" value="" autocomplete="new-password">
                    </div>
                    <div class="col-3">
                        <button id="btnGeneratePassword" type="button" class="btn btn-generate-password"> 生成强密码 </button>
                    </div>
                    <div class="col-3">

                    </div>
                </div>
            </div>
            <h3 class="card-title mt-1">备注</h3>
            <div>
                <div class="row g-2">
                    <div class="col">
                        <textarea name="remark" rows="2" class="form-control" placeholder="关于这个用户的一些备注" value="Mike">{{ user.remark }}</textarea>
                    </div>
                </div>
            </div>
            <input type="hidden" name="id" value="{{ user.id }}">
            <input type="hidden" name="avatar" value="{{ user.avatar }}">
        </div>
    </form>
</div>
<div class="card-footer bg-transparent mt-auto">
    <div class="btn-list justify-content-end">
        <button id="btnSaveUserAccountInfo" class="btn btn-primary"> 保存 </button>
    </div>
</div>
{% endblock %}
