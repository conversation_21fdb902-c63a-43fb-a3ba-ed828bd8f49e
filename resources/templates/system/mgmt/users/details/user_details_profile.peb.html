 {% extends "./layout-user-details.peb.html" %}
{% set title = "用户档案"|trans %}

{% block block_body %}
<script>
$(() => {

})
</script>
<div class="card-body p-2">
    <div id="errors">
    </div>
    <div class="page-wrapper">
        <!-- BEGIN PAGE HEADER -->
        <div class="page-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="avatar avatar-lg" style="background-image: url(./static/avatars/003m.jpg)"> </span>
                    </div>
                    <div class="col">
                        <h1 class="fw-bold m-0"><PERSON></h1>
                        <div class="my-2">Unemployed. Building a $1M solo business while traveling the world. Currently at $400k/yr.</div>
                        <div class="list-inline list-inline-dots text-secondary">
                            <div class="list-inline-item">
                                <!-- Download SVG icon from http://tabler.io/icons/icon/map -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-inline icon-2">
                                    <path d="M3 7l6 -3l6 3l6 -3v13l-6 3l-6 -3l-6 3v-13"></path>
                                    <path d="M9 4v13"></path>
                                    <path d="M15 7v13"></path>
                                </svg>
                                Harbin University of Civil Engineering &amp; Architecture, China
                            </div>
                            <div class="list-inline-item">
                                <!-- Download SVG icon from http://tabler.io/icons/icon/mail -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-inline icon-2">
                                    <path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z"></path>
                                    <path d="M3 7l9 6l9 -6"></path>
                                </svg>
                                <a href="#" class="text-reset"><EMAIL></a>
                            </div>
                            <div class="list-inline-item">
                                <!-- Download SVG icon from http://tabler.io/icons/icon/cake -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-inline icon-2">
                                    <path d="M3 20h18v-8a3 3 0 0 0 -3 -3h-12a3 3 0 0 0 -3 3v8z"></path>
                                    <path d="M3 14.803c.312 .135 .654 .204 1 .197a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1c.35 .007 .692 -.062 1 -.197"></path>
                                    <path d="M12 4l1.465 1.638a2 2 0 1 1 -3.015 .099l1.55 -1.737z"></path>
                                </svg>
                                15/10/1972
                            </div>
                        </div>
                    </div>
                    <div class="col-auto ms-auto">
                        <div class="btn-list">
                            <a href="#" class="btn btn-2 btn-icon" aria-label="Button">
                                <!-- Download SVG icon from http://tabler.io/icons/icon/dots -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2">
                                    <path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                    <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                    <path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                </svg>
                            </a>
                            <a href="#" class="btn btn-2 btn-icon" aria-label="Button">
                                <!-- Download SVG icon from http://tabler.io/icons/icon/message -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2">
                                    <path d="M8 9h8"></path>
                                    <path d="M8 13h6"></path>
                                    <path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"></path>
                                </svg>
                            </a>
                            <a href="#" class="btn btn-primary btn-3">
                                <!-- Download SVG icon from http://tabler.io/icons/icon/check -->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2">
                                    <path d="M5 12l5 5l10 -10"></path>
                                </svg>
                                Following
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END PAGE HEADER -->
        <!-- BEGIN PAGE BODY -->
        <div class="page-body">
            <div class="container-xl">
                <div class="row g-3">
                    <div class="col">
                        <ul class="timeline">
                            <li class="timeline-event">
                                <div class="timeline-event-icon bg-x-lt">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/brand-x -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                        <path d="M4 4l11.733 16h4.267l-11.733 -16z"></path>
                                        <path d="M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772"></path>
                                    </svg>
                                </div>
                                <div class="card timeline-event-card">
                                    <div class="card-body">
                                        <div class="text-secondary float-end">10 hrs ago</div>
                                        <h4>+1150 Followers</h4>
                                        <p class="text-secondary">You’re getting more and more followers, keep it up!</p>
                                    </div>
                                </div>
                            </li>
                            <li class="timeline-event">
                                <div class="timeline-event-icon">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/briefcase -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                        <path d="M3 7m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v9a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                                        <path d="M8 7v-2a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v2"></path>
                                        <path d="M12 12l0 .01"></path>
                                        <path d="M3 13a20 20 0 0 0 18 0"></path>
                                    </svg>
                                </div>
                                <div class="card timeline-event-card">
                                    <div class="card-body">
                                        <div class="text-secondary float-end">2 hrs ago</div>
                                        <h4>+3 New Products were added!</h4>
                                        <p class="text-secondary">Congratulations!</p>
                                    </div>
                                </div>
                            </li>
                            <li class="timeline-event">
                                <div class="timeline-event-icon">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/check -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                        <path d="M5 12l5 5l10 -10"></path>
                                    </svg>
                                </div>
                                <div class="card timeline-event-card">
                                    <div class="card-body">
                                        <div class="text-secondary float-end">1 day ago</div>
                                        <h4>Database backup completed!</h4>
                                        <p class="text-secondary">Download the <a href="#">latest backup</a>.</p>
                                    </div>
                                </div>
                            </li>
                            <li class="timeline-event">
                                <div class="timeline-event-icon bg-facebook-lt">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/brand-facebook -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                        <path d="M7 10v4h3v7h4v-7h3l1 -4h-4v-2a1 1 0 0 1 1 -1h3v-4h-3a5 5 0 0 0 -5 5v2h-3"></path>
                                    </svg>
                                </div>
                                <div class="card timeline-event-card">
                                    <div class="card-body">
                                        <div class="text-secondary float-end">1 day ago</div>
                                        <h4>+290 Page Likes</h4>
                                        <p class="text-secondary">This is great, keep it up!</p>
                                    </div>
                                </div>
                            </li>
                            <li class="timeline-event">
                                <div class="timeline-event-icon">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/user-plus -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                        <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"></path>
                                        <path d="M16 19h6"></path>
                                        <path d="M19 16v6"></path>
                                        <path d="M6 21v-2a4 4 0 0 1 4 -4h4"></path>
                                    </svg>
                                </div>
                                <div class="card timeline-event-card">
                                    <div class="card-body">
                                        <div class="text-secondary float-end">2 days ago</div>
                                        <h4>+3 Friend Requests</h4>
                                        <div class="avatar-list mt-3">
                                            <span class="avatar avatar-2" style="background-image: url(./static/avatars/000m.jpg)"><span class="badge bg-success"></span> </span>
                                            <span class="avatar avatar-2" style="background-image: url(./static/avatars/052f.jpg)"><span class="badge bg-success"></span> </span>
                                            <span class="avatar avatar-2" style="background-image: url(./static/avatars/002m.jpg)"><span class="badge bg-success"></span> </span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="timeline-event">
                                <div class="timeline-event-icon">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/photo -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                        <path d="M15 8h.01"></path>
                                        <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>
                                        <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>
                                        <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>
                                    </svg>
                                </div>
                                <div class="card timeline-event-card">
                                    <div class="card-body">
                                        <div class="text-secondary float-end">3 days ago</div>
                                        <h4>+3 New photos</h4>
                                        <div class="mt-3">
                                            <div class="row g-2">
                                                <div class="col-4">
                                                    <!-- Photo -->
                                                    <img src="./static/photos/blue-sofa-with-pillows-in-a-designer-living-room-interior.jpg" class="rounded" alt="Blue sofa with pillows in a designer living room interior">
                                                </div>
                                                <div class="col-4">
                                                    <!-- Photo -->
                                                    <img src="./static/photos/home-office-desk-with-macbook-iphone-calendar-watch-and-organizer.jpg" class="rounded" alt="Home office desk with Macbook, iPhone, calendar, watch &amp; organizer">
                                                </div>
                                                <div class="col-4">
                                                    <!-- Photo -->
                                                    <img src="./static/photos/young-woman-working-in-a-cafe.jpg" class="rounded" alt="Young woman working in a cafe">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="timeline-event">
                                <div class="timeline-event-icon">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/settings -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                                        <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                                        <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"></path>
                                    </svg>
                                </div>
                                <div class="card timeline-event-card">
                                    <div class="card-body">
                                        <div class="text-secondary float-end">2 weeks ago</div>
                                        <h4>System updated to v2.02</h4>
                                        <p class="text-secondary">Check the complete changelog at the <a href="#">activity page</a>.</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="col-lg-4">
                        <div class="row row-cards">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="card-title">Basic info</div>
                                        <div class="mb-2">
                                            <!-- Download SVG icon from http://tabler.io/icons/icon/book -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon me-2 text-secondary icon-2">
                                                <path d="M3 19a9 9 0 0 1 9 0a9 9 0 0 1 9 0"></path>
                                                <path d="M3 6a9 9 0 0 1 9 0a9 9 0 0 1 9 0"></path>
                                                <path d="M3 6l0 13"></path>
                                                <path d="M12 6l0 13"></path>
                                                <path d="M21 6l0 13"></path>
                                            </svg>
                                            Went to: <strong>University of Ljubljana</strong>
                                        </div>
                                        <div class="mb-2">
                                            <!-- Download SVG icon from http://tabler.io/icons/icon/briefcase -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon me-2 text-secondary icon-2">
                                                <path d="M3 7m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v9a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                                                <path d="M8 7v-2a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v2"></path>
                                                <path d="M12 12l0 .01"></path>
                                                <path d="M3 13a20 20 0 0 0 18 0"></path>
                                            </svg>
                                            Worked at: <strong>Devpulse</strong>
                                        </div>
                                        <div class="mb-2">
                                            <!-- Download SVG icon from http://tabler.io/icons/icon/home -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon me-2 text-secondary icon-2">
                                                <path d="M5 12l-2 0l9 -9l9 9l-2 0"></path>
                                                <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                                                <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                                            </svg>
                                            Lives in: <strong>Šentilj v Slov. Goricah, Slovenia</strong>
                                        </div>
                                        <div class="mb-2">
                                            <!-- Download SVG icon from http://tabler.io/icons/icon/map-pin -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon me-2 text-secondary icon-2">
                                                <path d="M9 11a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"></path>
                                                <path d="M17.657 16.657l-4.243 4.243a2 2 0 0 1 -2.827 0l-4.244 -4.243a8 8 0 1 1 11.314 0z"></path>
                                            </svg>
                                            From: <strong><span class="flag flag-xs flag-country-si"></span> Slovenia</strong>
                                        </div>
                                        <div class="mb-2">
                                            <!-- Download SVG icon from http://tabler.io/icons/icon/calendar -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon me-2 text-secondary icon-2">
                                                <path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z"></path>
                                                <path d="M16 3v4"></path>
                                                <path d="M8 3v4"></path>
                                                <path d="M4 11h16"></path>
                                                <path d="M11 15h1"></path>
                                                <path d="M12 15v3"></path>
                                            </svg>
                                            Birth date: <strong>13/01/1985</strong>
                                        </div>
                                        <div>
                                            <!-- Download SVG icon from http://tabler.io/icons/icon/clock -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon me-2 text-secondary icon-2">
                                                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
                                                <path d="M12 7v5l3 3"></path>
                                            </svg>
                                            Time zone: <strong>Europe/Ljubljana</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <h2 class="card-title">About Me</h2>
                                        <div>
                                            <p>
                                                Lorem ipsum dolor sit amet, consectetur adipisicing elit. Accusantium aliquid beatae eaque eius esse fugit, hic id illo itaque modi
                                                molestias nemo perferendis quae rerum soluta. Blanditiis laborum minima molestiae molestias nemo nesciunt nisi pariatur quae
                                                sapiente ut. Aut consectetur doloremque, error impedit, ipsum labore laboriosam minima non omnis perspiciatis possimus praesentium
                                                provident quo recusandae suscipit tempore totam.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END PAGE BODY -->
        <!--  BEGIN FOOTER  -->
        <footer class="footer footer-transparent d-print-none">
            <div class="container-xl">
                <div class="row text-center align-items-center flex-row-reverse">
                    <div class="col-lg-auto ms-lg-auto">
                        <ul class="list-inline list-inline-dots mb-0">
                            <li class="list-inline-item"><a href="https://tabler.io/docs" target="_blank" class="link-secondary" rel="noopener">Documentation</a></li>
                            <li class="list-inline-item"><a href="./license.html" class="link-secondary">License</a></li>
                            <li class="list-inline-item">
                                <a href="https://github.com/tabler/tabler" target="_blank" class="link-secondary" rel="noopener">Source code</a>
                            </li>
                            <li class="list-inline-item">
                                <a href="https://github.com/sponsors/codecalm" target="_blank" class="link-secondary" rel="noopener">
                                    <!-- Download SVG icon from http://tabler.io/icons/icon/heart -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-pink icon-inline icon-4">
                                        <path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572"></path>
                                    </svg>
                                    Sponsor
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                        <ul class="list-inline list-inline-dots mb-0">
                            <li class="list-inline-item">
                                Copyright © 2025
                                <a href="." class="link-secondary">Tabler</a>. All rights reserved.
                            </li>
                            <li class="list-inline-item">
                                <a href="./changelog.html" class="link-secondary" rel="noopener"> v1.2.0 </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </footer>
        <!--  END FOOTER  -->
    </div>
</div>
{% endblock %}
