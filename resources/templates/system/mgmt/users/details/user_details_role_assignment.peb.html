{% extends "./layout-user-details.peb.html" %}
{% set title = "用户角色分配"|trans %}

{% block block_body %}
<div class="card-body">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h3 class="card-title mb-0">角色分配</h3>
            <p class="text-secondary m-0">为用户 {{ user.displayName }}({{ user.login }}) 分配角色</p>
        </div>
        <div class="btn-list">
            <button type="button" class="btn btn-primary" id="btn-save-roles">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M5 12l5 5l10 -10"/>
                </svg>
                保存
            </button>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <h4>可用角色</h4>
            <div class="list-group" id="available-roles">
                {% for role in allRoles %}
                <div class="list-group-item d-flex align-items-center" data-role-id="{{ role.id }}">
                    <div class="form-check">
                        <input class="form-check-input role-checkbox" type="checkbox"
                               value="{{ role.id }}"
                               id="role-{{ role.id }}"
                               {% set isChecked = false %}
                               {% for userRole in userRoles %}
                                   {% if userRole.id == role.id %}
                                       {% set isChecked = true %}
                                   {% endif %}
                               {% endfor %}
                               {% if isChecked %}checked{% endif %}
                               {% if role.disabled %}disabled{% endif %}>
                        <label class="form-check-label" for="role-{{ role.id }}">
                            <div>
                                <strong>{{ role.displayName }}</strong>
                                <small class="text-muted d-block">{{ role.name }}</small>
                                {% if role.remark %}
                                <small class="text-muted d-block">{{ role.remark }}</small>
                                {% endif %}
                            </div>
                        </label>
                    </div>
                    {% if role.disabled %}
                    <span class="badge bg-secondary ms-auto">已禁用</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="col-md-6">
            <h4>已分配角色</h4>
            <div class="list-group" id="assigned-roles">
                {% for role in userRoles %}
                <div class="list-group-item d-flex align-items-center">
                    <div>
                        <strong>{{ role.displayName }}</strong>
                        <small class="text-muted d-block">{{ role.name }}</small>
                        {% if role.remark %}
                        <small class="text-muted d-block">{{ role.remark }}</small>
                        {% endif %}
                    </div>
                    {% if role.name == 'ROLE_ADMIN' and user.isBuiltinAdmin %}
                    <span class="badge bg-warning ms-auto">系统角色</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const userId = {{ user.id }};
    const saveBtn = document.getElementById('btn-save-roles');
    const roleCheckboxes = document.querySelectorAll('.role-checkbox');
    
    saveBtn.addEventListener('click', function() {
        const selectedRoleIds = Array.from(roleCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => parseInt(cb.value));
            
        const data = {
            userId: userId,
            roleIds: selectedRoleIds
        };
        
        // 禁用按钮
        saveBtn.disabled = true;
        
        fetch(`/admiz/api/system/mgmt/users/roles?userId=${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            console.log('API Response:', result);
            if (result.succeed || result.success) {
                // showToast('success', result.msg || '角色分配成功');
                window.location.reload();
            } else {
                // showToast('error', result.msg || result.message || '角色分配失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', '操作失败，请重试');
        })
        .finally(() => {
            // 恢复按钮状态
            saveBtn.disabled = false;
        });
    });
    
    function showToast(type, message) {
        alert(message);
    }
});
</script>
{% endblock %}