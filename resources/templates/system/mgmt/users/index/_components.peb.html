<script>
    $(() => {
        // let callbacks = {
        //     '#userDetailsDialog .btn-submit': function (e, $modal) {
        //         console.log('#userDetailsDialog .btn-submit: ', e)
        //     },
        // }
        //
        // $('#superModal').click(() => {
        //     $('#userDetailsDialog').modal({keyboard: true}, callbacks).modal('show')
        // });

    })
</script>

{#<form class="card" >#}
{#    <div class="card-header">#}
{#        <h3 class="card-title">Horizontal form</h3>#}
{#    </div>#}
{#    <div class="card-body">#}
{#        <div class="mb-3 row">#}
{#            <label class="col-3 col-form-label required">Email address</label>#}
{#            <div class="col">#}
{#                <input type="email" class="form-control" aria-describedby="emailHelp" placeholder="Enter email">#}
{#                <small class="form-hint">We'll never share your email with anyone else.</small>#}
{#            </div>#}
{#        </div>#}
{#        <div class="mb-3 row">#}
{#            <label class="col-3 col-form-label required">Password</label>#}
{#            <div class="col">#}
{#                <input type="password" class="form-control" placeholder="Password">#}
{#                <small class="form-hint">#}
{#                    Your password must be 8-20 characters long, contain letters and numbers, and must not contain spaces, special characters, or#}
{#                    emoji.#}
{#                </small>#}
{#            </div>#}
{#        </div>#}
{#        <div class="mb-3 row">#}
{#            <label class="col-3 col-form-label">Select</label>#}
{#            <div class="col">#}
{#                <select class="form-select">#}
{#                    <option>Option 1</option>#}
{#                    <optgroup label="Optgroup 1">#}
{#                        <option>Option 1</option>#}
{#                        <option>Option 2</option>#}
{#                    </optgroup>#}
{#                    <option>Option 2</option>#}
{#                    <optgroup label="Optgroup 2">#}
{#                        <option>Option 1</option>#}
{#                        <option>Option 2</option>#}
{#                    </optgroup>#}
{#                    <optgroup label="Optgroup 3">#}
{#                        <option>Option 1</option>#}
{#                        <option>Option 2</option>#}
{#                    </optgroup>#}
{#                    <option>Option 3</option>#}
{#                    <option>Option 4</option>#}
{#                </select>#}
{#            </div>#}
{#        </div>#}
{#        <div class="row">#}
{#            <label class="col-3 col-form-label pt-0">Checkboxes</label>#}
{#            <div class="col">#}
{#                <label class="form-check">#}
{#                    <input class="form-check-input" type="checkbox" checked="">#}
{#                    <span class="form-check-label">Option 1</span>#}
{#                </label>#}
{#                <label class="form-check">#}
{#                    <input class="form-check-input" type="checkbox">#}
{#                    <span class="form-check-label">Option 2</span>#}
{#                </label>#}
{#                <label class="form-check">#}
{#                    <input class="form-check-input" type="checkbox" disabled="">#}
{#                    <span class="form-check-label">Option 3</span>#}
{#                </label>#}
{#            </div>#}
{#        </div>#}
{#        <div class="text-end">#}
{#            <button type="submit" class="btn btn-primary">Submit</button>#}
{#        </div>#}
{#    </div>#}
{#</form>#}

<div class="modal rounded py-6" id="commonModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ "用户详情"|trans }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div id="userDetailsDialogModalBody" class="modal-body p-2">

            </div>
            <div class="modal-footer">
                <a href="#" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal"> 关闭 </a>
            </div>
        </div>
    </div>
</div>

<div class="modal rounded py-6" id="userDetailsDialog" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ "用户详情"|trans }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-2">
                <div class="card">
                    <div class="row g-0">
                        <div class="col-12 col-md-3 border-end">
                            <div class="card-body">
                                <h4 class="subheader">基本信息</h4>
                                <div class="list-group list-group-transparent">
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center active">账号信息</a>
                                </div>
                                <h4 class="subheader mt-4">其他信息</h4>
                                <div class="list-group list-group-transparent">
                                    <a href="#" class="list-group-item list-group-item-action">活动记录</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-9 d-flex flex-column">
                            <div class="card-body">
                                <h3 class="card-title">基本信息</h3>
                                <div class="row g-3">
                                    <div class="col-md">
                                        <div class="form-label">用户名</div>
                                        <input type="text" class="form-control " disabled value="用户名">
                                    </div>
                                    <div class="col-md">
                                        <div class="form-label">手机号</div>
                                        <input type="text" class="form-control" value="13800138000">
                                    </div>
                                </div>
                                <h3 class="card-title mt-4">邮箱</h3>
                                <div>
                                    <div class="row g-2">
                                        <div class="col-auto">
                                            <input type="text" class="form-control w-auto" value="<EMAIL>">
                                        </div>
                                        <div class="col-auto">
                                        </div>
                                    </div>
                                </div>
                                <h3 class="card-title mt-4">密码</h3>
                                <p class="card-subtitle">输入两次新密码来修改密码，留空则不修改</p>
                                <div>
                                    <div class="row g-2">
                                        <div class="col-auto">
                                            <input type="password" class="form-control w-auto" value="">
                                        </div>
                                        <div class="col-auto">
                                            <input type="password" class="form-control w-auto" value="">
                                        </div>
                                        <div class="col-auto">
                                            <a href="#" class="btn btn-1"> 生成强密码 </a>
                                        </div>
                                    </div>
                                </div>



                            </div>
                            <div class="card-footer bg-transparent mt-auto">
                                <div class="btn-list justify-content-end">
                                    <a href="#" class="btn btn-primary btn-2"> 保存 </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal"> 关闭 </a>
            </div>
        </div>
    </div>
</div>
