<div class="card">
    <div class="card-stamp">
        <div class="card-stamp-icon bg-white text-primary"><i class="ti ti-star"></i></div>
    </div>
    <div class="card-body p-2">
        <div class="container-fluid">
            <form method="get" action="" class="row m-1" id="filterForm">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text">搜索</span>
                        <input type="text" class="form-control" name="searchText" value="{{ userIndexVo.searchText }}" placeholder="（名字/用户名/手机号）" autocomplete="off">
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">状态</span>
                        <select name="locked" class="form-select">
                            <option {{ userIndexVo.locked is empty   ? 'selected' : '' }} value="">所有</option>
                            <option {{ userIndexVo.locked == 'false'  ? 'selected' : '' }} value="false">正常</option>
                            <option {{ userIndexVo.locked == 'true' ? 'selected' : '' }} value="true">封禁</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <button id="btnSubmit" type="submit" class="btn btn-primary btn-4">
                        <i class="ti ti-search"></i> 搜索
                    </button>
                </div>
                <input type="hidden" name="page" id="pageIndex" value="{{ page.getNumber() }}" />
                <input type="hidden" name="size"  id="pageSize"  value="{{ page.getSize() }}" />
                <input type="hidden" name="sort"  id="sort"  value="{{ sort.formValue }}" />
                <input type="hidden" name="rnd"   id="rnd"  value="{{ random() }}" />
            </form>
        </div>
    </div>
</div>
<script>
    // $(() => {
    //     $('#btnSubmit').click((e) => {
    //         console.log('btnSubmit clicked');
    //         $('#filterForm').submit();
    //     });
    // })
</script>
