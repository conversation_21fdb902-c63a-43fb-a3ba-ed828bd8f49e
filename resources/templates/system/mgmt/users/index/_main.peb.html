<table class="table table-vcenter table-selectable">
    <thead>
    <tr>
        <th class="w-1">
            <input id="chkCheckAll" class="form-check-input m-0 align-middle table-selectable-check" type="checkbox">
        </th>
        <th>
            <button class="table-sort d-flex {{ sort.direction }}" data-field="{{ sort.property }}">用户名</button>
        </th>
        <th>
            手机号
        </th>
        <th>
            状态
        </th>
        <th>
            角色
        </th>
        <th>
            最后登录时间
        </th>
        <th class="w-12">
            操作
        </th>
    </tr>
    </thead>
    <tbody class="table-tbody">
    {% if not page.hasContent() %}
    <tr>
        <td colspan="100%" class="text-danger text-xxl-center">
            NO DATA
        </td>
    </tr>
    {% else %}
        {% for user in page.getContent() %}
        <tr data-user-id="{{ user.id }}">
            <td>
                <input name="userIds" value="{{ user.id }}" class="form-check-input m-0 align-middle table-selectable-check" type="checkbox" aria-label="Select user">
            </td>
            <td class="sort-name">
                <a href="#" data-user-id="{{ user.id }}" class="btn-edit-user">
                    <span class="user-login">{{ user.login }}</span>
                </a>
                <div class="user-display-name" style="display: none;">{{ user.displayName }}</div>
            </td>
            <td class="sort-city">{{ user.mobile }}</td>
            <td class="sort-status">
                <span class="badge {{ user.locked ? 'bg-danger-lt' : 'bg-success-lt' }}">{{ user.locked ? '锁定' : '正常' }}</span>
            </td>
            <td class="sort-tags">
                <div class="role-list">
                    {% if user.roles is defined and user.roles is not empty %}
                        {% set roleNames = [] %}
                        {% for role in user.roles %}
                            {% if not role.disabled %}
                                {% set roleNames = roleNames + [role.displayName] %}
                            {% endif %}
                        {% endfor %}
                        {% if roleNames is not empty %}
                            {{ roleNames|join(', ') }}
                        {% else %}
                            <span class="text-muted">无角色</span>
                        {% endif %}
                    {% else %}
                        <span class="text-muted">无角色</span>
                    {% endif %}
                </div>
            </td>
            <td class="sort-date">{{ user.realm }}</td>
            <td class="sort-category py-0">
                <div class="btn-list flex-nowrap">
                    <button data-user-id="{{ user.id }}" class="btn btn-1 btn-edit-user"> 编辑 </button>
                    <div class="dropdown">
                        <button class="btn dropdown-toggle align-text-top" data-bs-toggle="dropdown" aria-expanded="false">
                            操作
                        </button>
                        <div class="dropdown-menu dropdown-menu-end" style="">
<!--                            <a class="dropdown-item" href="/admiz/system/mgmt/users/{{ user.id }}/roles"> 分配角色 </a>-->
                            <a class="dropdown-item" href="#"> 锁定 </a>
                            <a class="dropdown-item" href="#"> 重置密码 </a>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        {% endfor %}
    {% endif %}
    </tbody>
</table>

<script>
function getOrder($btn) {
    var predefinedOrder = $btn.data('order');

    if (predefinedOrder === 'asc' || predefinedOrder === 'desc') {
        return predefinedOrder;
    }

    return $btn.hasClass('asc') ? 'desc' : 'asc';
}

function setOrder($btn, order) {
    $btn.removeClass('asc')
        .removeClass('desc')
        .addClass(order);
}

$(() => {
    $('.btn-edit-user').click((e) => {
        let userId = $(e.target).data('user-id');
        $.openGlobalIFrameModal('/admiz/system/mgmt/users/details/account?userId=' + userId).modal('show');
    });

    $('button.table-sort').click((e) => {
        let $btn = $(e.target);
        let newOrder = getOrder($btn);
        setOrder($btn, newOrder);

        let field = $btn.data('field');
        $('#sort').val(`${field},${newOrder}`);
        $('#filterForm').submit();
    });

    $('#chkCheckAll').click((e) => {
        let $chkCheckAll = $(e.target);
        let isCheckAll = $chkCheckAll.is(':checked');

        $('input[name=userIds].form-check-input').prop('checked', isCheckAll);
    });

    $('input[name=userIds].form-check-input').click((e) => {
        let totalCheckboxes = $('input[name=userIds].form-check-input').length;
        let checkedCheckboxes = $('input[name=userIds].form-check-input:checked').length;
        $('#chkCheckAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });
})
</script>
