{% extends "/layout/layout-content-embed.peb.html" %}
{% set title = "用户角色分配"|trans %}

{% block block_body %}
<div class="card">
    <div class="card-header">
        <div class="row w-full">
            <div class="col">
                <h3 class="card-title mb-0">用户角色分配</h3>
                <p class="text-secondary m-0">为用户 {{ user.displayName }}({{ user.login }}) 分配角色</p>
            </div>
            <div class="col-md-auto col-sm-12">
                <div class="ms-auto d-flex flex-wrap btn-list">
                    <button type="button" class="btn btn-primary" id="btn-save-roles">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l5 5l10 -10"/>
                        </svg>
                        保存
                    </button>
                    <button type="button" class="btn btn-secondary" id="btn-back">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M9 11l-4 4l4 4m-4 -4h11a4 4 0 0 0 0 -8h-1"/>
                        </svg>
                        返回
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h4>可用角色</h4>
                <div class="list-group" id="available-roles">
                    {% for role in allRoles %}
                    <div class="list-group-item d-flex align-items-center" data-role-id="{{ role.id }}">
                        <div class="form-check">
                            <input class="form-check-input role-checkbox" type="checkbox"
                                   value="{{ role.id }}"
                                   id="role-{{ role.id }}"
                                   {% set isChecked = false %}
                                   {% for userRole in userRoles %}
                                       {% if userRole.id == role.id %}
                                           {% set isChecked = true %}
                                       {% endif %}
                                   {% endfor %}
                                   {% if isChecked %}checked{% endif %}
                                   {% if role.disabled %}disabled{% endif %}>
                            <label class="form-check-label" for="role-{{ role.id }}">
                                <div>
                                    <strong>{{ role.displayName }}</strong>
                                    <small class="text-muted d-block">{{ role.name }}</small>
                                    {% if role.remark %}
                                    <small class="text-muted d-block">{{ role.remark }}</small>
                                    {% endif %}
                                </div>
                            </label>
                        </div>
                        {% if role.disabled %}
                        <span class="badge bg-secondary ms-auto">已禁用</span>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="col-md-6">
                <h4>已分配角色</h4>
                <div class="list-group" id="assigned-roles">
                    {% for role in userRoles %}
                    <div class="list-group-item d-flex align-items-center">
                        <div>
                            <strong>{{ role.displayName }}</strong>
                            <small class="text-muted d-block">{{ role.name }}</small>
                            {% if role.remark %}
                            <small class="text-muted d-block">{{ role.remark }}</small>
                            {% endif %}
                        </div>
                        {% if role.name == 'ROLE_ADMIN' and user.isBuiltinAdmin %}
                        <span class="badge bg-warning ms-auto">系统角色</span>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const userId = {{ user.id }};
    const saveBtn = document.getElementById('btn-save-roles');
    const backBtn = document.getElementById('btn-back');
    const roleCheckboxes = document.querySelectorAll('.role-checkbox');

    backBtn.addEventListener('click', function() {
        if (window.self !== window.top) {
            try {
                // 如果父窗口有 Bootstrap 模态框管理器
                if (window.parent && window.parent.$ && window.parent.$('.modal.show').length > 0) {
                    window.parent.$('.modal.show').modal('hide');
                    return;
                }

                // 如果使用了全局模态框管理器
                if (window.parent && window.parent.$ && window.parent.$.closeGlobalIFrameModal) {
                    window.parent.$.closeGlobalIFrameModal();
                    return;
                }

                // 触发父窗口的关闭事件
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({ action: 'closeModal' }, '*');
                    return;
                }

                window.close();
            } catch (e) {
                console.error('关闭模态框失败:', e);
                window.location.href = '/admiz/system/mgmt/users';
            }
        } else {
            window.location.href = '/admiz/system/mgmt/users';
        }
    });
    
    saveBtn.addEventListener('click', function() {
        const selectedRoleIds = Array.from(roleCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => parseInt(cb.value));
            
        const data = {
            userId: userId,
            roleIds: selectedRoleIds
        };
        
        fetch(`/admiz/api/system/mgmt/users/roles?userId=${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            console.log('API Response:', result);
            if (result.succeed || result.success) {
                // showToast('success', result.msg || '角色分配成功');
                setTimeout(() => {
                    // 保存成功后的返回逻辑与返回按钮相同
                    if (window.self !== window.top) {
                        try {
                            if (window.parent && window.parent.$ && window.parent.$('.modal.show').length > 0) {
                                window.parent.$('.modal.show').modal('hide');
                                setTimeout(() => {
                                    window.parent.location.reload();
                                }, 100);
                                return;
                            }
                            
                            if (window.parent && window.parent.$ && window.parent.$.closeGlobalIFrameModal) {
                                window.parent.$.closeGlobalIFrameModal();
                                setTimeout(() => {
                                    window.parent.location.reload();
                                }, 300);
                                return;
                            }
                            
                            if (window.parent && window.parent.postMessage) {
                                window.parent.postMessage({ action: 'closeModalAndRefresh' }, '*');
                                return;
                            }
                        } catch (e) {
                            console.error('关闭模态框失败:', e);
                            window.location.reload();
                        }
                    } else {
                        window.location.reload();
                    }
                }, 300);
            } else {
                showToast('error', result.msg || result.message || '角色分配失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', '操作失败，请重试');
        });
    });
    
    function showToast(type, message) {
        alert(message);
    }
});
</script>
{% endblock %}