import org.gradle.kotlin.dsl.maven

pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
        maven {
            url = uri("http://nexus.dev.allinpaygx.cn/repository/maven/")
            isAllowInsecureProtocol = true
        }
    }

    plugins {
        java
        groovy
        `java-library`
        id("org.springframework.boot") version "3.4.5"
        id("io.spring.dependency-management") version "1.1.7"
        id("com.netflix.dgs.codegen") version "7.0.3"
//    id("org.graalvm.buildtools.native") version "0.10.6"
    }
}

include("admiz")

include("tools")

include("common")
